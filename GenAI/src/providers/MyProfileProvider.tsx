import {
  SubscriptionPlanPublic,
  UserPublicDetail,
  subscriptionPlanGetSubscriptionPlanApi,
  usersReadCurrentUser,
} from '@/apis/client'
import { useAuth } from '@/hooks/useAuth'
import i18n from '@/i18n'
import { createContext, useEffect, useMemo, useState } from 'react'

export type Context = {
  myProfile: UserPublicDetail | undefined | null
  setMyProfile: (newProfile: UserPublicDetail) => void
  fetchMyProfile: () => Promise<void>
  isSuperAdmin: boolean
  loadingProfile: boolean
  mySubscriptionPlan?: SubscriptionPlanPublic | null
  setSubscriptionPlan: (newSubscriptionPlan: SubscriptionPlanPublic) => void
  fetchMySubscriptionPlan: () => Promise<void>
}

export const MyProfileContext = createContext<Context>({} as Context)

const MyProfileProvider = ({ children }: any) => {
  const { accessToken } = useAuth()
  const [myProfile, setMyProfile] = useState<UserPublicDetail>()
  const [mySubscriptionPlan, setSubscriptionPlan] =
    useState<SubscriptionPlanPublic>()
  const [loadingProfile, setLoadingProfile] = useState(true)

  const fetchMyProfile = async () => {
    try {
      setLoadingProfile(true)
      const { data }: any = await usersReadCurrentUser()

      if (data?.data) {
        setMyProfile(data?.data)
        i18n.changeLanguage(data?.data?.language || 'en')
      }
    } finally {
      setLoadingProfile(false)
    }
  }

  const fetchMySubscriptionPlan = async () => {
    try {
      const { data }: any = await subscriptionPlanGetSubscriptionPlanApi()

      if (data?.data) {
        setSubscriptionPlan(data?.data)
      }
    } catch (error) {
      // Something went wrong
    }
  }

  useEffect(() => {
    if (accessToken) {
      fetchMyProfile()
    }
  }, [])

  const isSuperAdmin = useMemo(() => {
    return myProfile?.roles?.[0]?.name === 'SA'
  }, [myProfile])

  const contextValue: Context = useMemo(
    () => ({
      myProfile,
      setMyProfile,
      fetchMyProfile,
      isSuperAdmin,
      loadingProfile,
      mySubscriptionPlan,
      setSubscriptionPlan,
      fetchMySubscriptionPlan,
    }),
    [myProfile, loadingProfile, isSuperAdmin, mySubscriptionPlan]
  )

  return (
    <MyProfileContext.Provider value={contextValue}>
      {children}
    </MyProfileContext.Provider>
  )
}

export default MyProfileProvider
