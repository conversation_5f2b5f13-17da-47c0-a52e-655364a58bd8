import {
  OrganizationPublic,
  organizationsCreateOrganizationApi,
  organizationsLeaveOrganizationApi,
  organizationsReadMyOrganizationApi,
} from '@/apis/client'
import { MessageDialog } from '@/components/DialogMessage'
import useLayoutStore from '@/components/Layout/layoutStore'
import Message from '@/components/Message'
import { HTTP_STATUS_CODE } from '@/constants'
import { useMyProfile } from '@/hooks/useMyProfile'
import { isEmpty, isEqual } from 'lodash'
import { createContext, useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'

export type Context = {
  currentOrg?: OrganizationPublic
  listOrgs: OrganizationPublic[]
  switchOrg: (org?: OrganizationPublic) => void
  createOrg: (
    org: {
      name: string
      logo_file?: File
      groupSelected?: string[]
    },
    onSuccess?: () => void,
    onError?: () => void
  ) => void
  leaveOrg: (org: OrganizationPublic, onSuccess?: () => void) => void
  fetchMyOrgs: () => void
  isOrgOwner?: boolean
  isFetchingOrg: boolean
  resetStore: () => void
}

export const OrgContext = createContext<Context>({} as Context)

const OrgProvider = ({ children }: any) => {
  const { t } = useTranslation()
  const { myProfile } = useMyProfile()
  const { setLayoutLoading } = useLayoutStore((state) => ({
    setLayoutLoading: state.setLoading,
  }))

  const [currentOrg, setCurrentOrg] = useState<OrganizationPublic>()
  const [listOrgs, setListOrgs] = useState<OrganizationPublic[]>([])
  const [isFetchingOrg, setIsFetchingOrg] = useState(true)

  const isOrgOwner = useMemo(() => {
    return listOrgs?.some((org) => org.owner_id === myProfile?.id)
  }, [listOrgs, myProfile])

  const fetchMyOrgs = useCallback(async () => {
    try {
      setIsFetchingOrg(true)
      const res = await organizationsReadMyOrganizationApi()
      if (res?.status === HTTP_STATUS_CODE.SUCCESS && res.data?.data) {
        setListOrgs(res.data.data)
      } else {
        Message.error({ message: t('common.something_went_wrong') })
      }
    } catch (error) {
      Message.error({ message: t('common.something_went_wrong') })
    } finally {
      setIsFetchingOrg(false)
    }
  }, [t])

  const switchOrg = useCallback(
    (org?: OrganizationPublic) => {
      if (!org) {
        setCurrentOrg(undefined)
        return
      }
      if (!isEqual(currentOrg, org)) {
        setCurrentOrg(org)
      }
    },
    [currentOrg, listOrgs]
  )

  const createOrg = useCallback(
    async (
      org: {
        name: string
        logo_file?: File
        groupSelected?: string[]
      },
      onSuccess?: () => void,
      onError?: () => void
    ) => {
      try {
        if (!org) return

        const res = await organizationsCreateOrganizationApi({
          body: {
            name: org.name,
            logo_file: org.logo_file,
            group_names: JSON.stringify(org.groupSelected),
          },
        })

        if (res?.status === HTTP_STATUS_CODE.SUCCESS && res.data?.data) {
          setListOrgs((prev) => [...prev, res.data.data])
          setCurrentOrg(res.data.data)
          onSuccess?.()
          Message.success({ message: t('org.create_org_modal.create_success') })
        }
      } catch (error) {
        Message.error({ message: t('common.something_went_wrong') })
        onError?.()
      }
    },
    [t]
  )

  const leaveOrg = useCallback(
    (org: OrganizationPublic, onSuccess?: () => void) => {
      if (!org) return
      if (org?.owner_id === myProfile?.id) {
        Message.error({ message: t('org.leave_org.need_transfer_ownership') })
        return
      }

      MessageDialog.warning({
        mainMessage: t('org.leave_org.title'),
        subMessage: t('org.leave_org.description'),

        onClick: async () => {
          try {
            setLayoutLoading(true)

            const res = await organizationsLeaveOrganizationApi({
              path: {
                organization_id: listOrgs?.[0]?.id,
              },
            })

            if (res?.status === HTTP_STATUS_CODE.NO_CONTENT) {
              setListOrgs((prev) => prev.filter((o) => o.id !== org.id))
              if (isEqual(currentOrg, org)) {
                setCurrentOrg(undefined)
              }

              setLayoutLoading(false)

              fetchMyOrgs()
              onSuccess?.()
              Message.success({ message: t('org.leave_org.success') })
            }
          } catch (error) {
            Message.error({
              message: t('common.something_went_wrong'),
            })
          } finally {
            setLayoutLoading(false)
          }
        },
      })
    },
    [currentOrg, myProfile, listOrgs, fetchMyOrgs]
  )

  const resetStore = useCallback(() => {
    setCurrentOrg(undefined)
    setListOrgs([])
    setIsFetchingOrg(true)
  }, [])

  useEffect(() => {
    if (isEmpty(myProfile)) return
    fetchMyOrgs()
  }, [myProfile])

  useEffect(() => {
    if (currentOrg) {
      localStorage.setItem('orgId', `${currentOrg.id}`)
    } else {
      localStorage.removeItem('orgId')
    }
  }, [currentOrg])

  return (
    <OrgContext.Provider
      value={{
        currentOrg,
        listOrgs,
        switchOrg,
        createOrg,
        leaveOrg,
        isOrgOwner,
        fetchMyOrgs,
        isFetchingOrg,
        resetStore,
      }}
    >
      {children}
    </OrgContext.Provider>
  )
}

export default OrgProvider
