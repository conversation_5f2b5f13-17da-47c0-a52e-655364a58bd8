import Icon from '@/assets/icon/Icon'
import { getIconNameByFileExt } from '../helper'

interface Props {
  fileExt?: string
  size?: number
  className?: string
}

const IconFile = ({ fileExt, size = 48, className }: Props) => {
  if (!fileExt) return null

  return (
    <div className={className}>
      <Icon name={getIconNameByFileExt(fileExt)} size={size} />
    </div>
  )
}

export default IconFile
