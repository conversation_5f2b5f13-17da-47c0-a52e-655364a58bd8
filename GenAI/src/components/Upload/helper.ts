import { NameIcon } from '@/assets/icon/types'
import { EFileType } from './const'

export const getDataFormat = (accept: string) => {
  return accept
    .split(',')
    .map((type) => type.replace('.', ''))
    .join(', ')
}

export const getFileTypeByMime = (mime: string | null) => {
  if (!mime) return

  switch (mime) {
    case 'pdf':
    case 'application/pdf':
      return EFileType.pdf
    case 'docx':
    case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
      return EFileType.docx
    case 'doc':
    case 'application/msword':
    case 'application/doc':
    case 'application/ms-doc':
    case 'application/vnd.ms-word':
      return EFileType.doc
    case 'pptx':
    case 'application/vnd.openxmlformats-officedocument.presentationml.presentation':
      return EFileType.pptx
    case 'ppt':
    case 'application/vnd.ms-powerpoint':
    case 'application/mspowerpoint':
    case 'application/powerpoint':
    case 'application/x-mspowerpoint':
      return EFileType.ppt
    case 'txt':
    case 'text/plain':
      return EFileType.txt
    case 'csv':
    case 'text/csv':
    case 'xls':
    case 'application/vnd.ms-excel':
    case 'xlsx':
    case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
      return EFileType.csv
    case 'epub':
    case 'application/epub+zip':
      return EFileType.epub
    default:
      return EFileType.pdf
  }
}

export const getFileExt = (fileName: string) => {
  return fileName.split('.').pop()?.toLowerCase()
}

export const getIconNameByFileType = (fileType: EFileType): NameIcon => {
  switch (fileType) {
    case EFileType.pdf:
      return 'file_symbol-pdf'
    case EFileType.docx:
    case EFileType.doc:
      return 'file_symbol-word'
    case EFileType.pptx:
    case EFileType.ppt:
      return 'file_symbol-ppt'
    case EFileType.txt:
      return 'file_symbol-txt'
    case EFileType.csv:
      return 'file_symbol-csv'
    case EFileType.epub:
      return 'file_symbol-epub'
    default:
      return 'file_symbol-pdf'
  }
}

export const getIconNameByFileExt = (fileExt: string): NameIcon => {
  switch (fileExt) {
    case 'pdf':
      return 'file_symbol-pdf'
    case 'docx':
    case 'doc':
      return 'file_symbol-word'
    case 'pptx':
    case 'ppt':
      return 'file_symbol-ppt'
    case 'txt':
      return 'file_symbol-txt'
    case 'csv':
    case 'xls':
    case 'xlsx':
      return 'file_symbol-csv'
    case 'epub':
      return 'file_symbol-epub'
    case 'jpg':
    case 'jpeg':
    case 'png':
      return 'file_symbol-image'
    default:
      return 'file_symbol-error'
  }
}

export const getIconNameByFileIcon = (fileExt: string): NameIcon => {
  switch (fileExt) {
    case 'pdf':
      return 'file-icon-pdf'
    case 'docx':
    case 'doc':
      return 'file-icon-word'
    case 'pptx':
    case 'ppt':
      return 'file-icon-ppt'
    case 'txt':
      return 'file-icon-txt'
    case 'csv':
    case 'xls':
    case 'xlsx':
      return 'file-icon-csv'
    case 'epub':
      return 'file-icon-epub'
    case 'jpg':
    case 'jpeg':
    case 'png':
      return 'file_symbol-image'
    default:
      return 'file_symbol-error'
  }
}

export const getSizeIcon = (size: string) => {
  switch (size) {
    case 'smaller':
      return 24
    case 'small':
      return 28
    case 'medium':
      return 40
    case 'large':
      return 64
    default:
      break
  }
}

export const getTypeText = (size: string) => {
  switch (size) {
    case 'smaller':
      return 'helperText'
    case 'small':
      return 'helperText'
    case 'medium':
      return 'supportText'
    case 'large':
      return 'subBody'
    default:
      return 'subBody'
  }
}

export const getFileNameWithoutExtension = (filePath: string): string => {
  return filePath.replace(/\.[^/.]+$/, '')
}
