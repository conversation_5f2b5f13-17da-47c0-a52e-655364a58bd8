import Icon from '@/assets/icon/Icon'
import clsx from 'clsx'
import { memo, useMemo, useState } from 'react'

import { useMyProfile } from '@/hooks/useMyProfile'
import { Context } from '@/providers/SidebarProvider'
import { rootUrls } from '@/routes/rootUrls'
import { colors } from '@/theme'
import { useTranslation } from 'react-i18next'
import { Link } from 'react-router-dom'
import Avatar from '../Avatar'
import IconButton from '../IconButton'
import OrgListInSidebar from '../Org/OrgListInSidebar'
import Popover from '../Popover'
import Text from '../Text'
import GroupMenu from './GroupMenu'
import SidebarPopoverContent from './SidebarPopoverContent'
import IconConnection from './icons/IconConnection'

const Sidebar = ({ isExpanded, setExpanded }: Context) => {
  const { t } = useTranslation()
  const { isSuperAdmin, myProfile } = useMyProfile()
  const [isOpenAccSettings, setIsOpenAccSettings] = useState(false)

  const menuGroupItems: { [key: string]: any } = useMemo(() => {
    const groups = {
      main: [
        {
          path: '/',
          icon: (
            <Icon
              name="home-smile"
              size={20}
              color={colors['Tertiary-Color']}
              className="min-w-5"
            />
          ),
          iconActive: (
            <Icon
              name="home-smile"
              size={20}
              color={colors['Primary-Color']}
              className="min-w-5"
            />
          ),
          name: t('sidebar.home'),
          isExact: true,
        },
        {
          path: '/dashboard',
          icon: (
            <Icon
              name="bar-chart-square-02"
              size={20}
              color={colors['Tertiary-Color']}
            />
          ),
          iconActive: (
            <Icon
              name="bar-chart-square-02"
              size={20}
              color={colors['Primary-Color']}
            />
          ),
          name: t('sidebar.dashboard'),
        },
        {
          path: '/applications',
          icon: (
            <Icon
              name="grid-01"
              size={20}
              color={colors['Tertiary-Color']}
              className="min-w-5"
            />
          ),
          iconActive: (
            <Icon
              name="grid-01"
              size={20}
              color={colors['Primary-Color']}
              className="min-w-5"
            />
          ),
          name: t('sidebar.applications'),
        },

        {
          path: '/marketplace',
          icon: (
            <Icon
              name="building-02"
              size={20}
              color={colors['Tertiary-Color']}
              className="min-w-5"
            />
          ),
          iconActive: (
            <Icon
              name="building-02"
              size={20}
              color={colors['Primary-Color']}
              className="min-w-5"
            />
          ),
          name: t('sidebar.marketplace'),
          children: [
            {
              path: '/workflows',
              name: t('sidebar.workflows'),
              icon: (
                <Icon
                  name="dataflow-03"
                  size={20}
                  color={colors['Tertiary-Color']}
                  className="min-w-5"
                />
              ),
              iconActive: (
                <Icon
                  name="dataflow-03"
                  size={20}
                  color={colors['Primary-Color']}
                  className="min-w-5"
                />
              ),
              isDefault: true,
            },
            {
              path: '/tools',
              name: t('sidebar.tools'),
              icon: (
                <Icon
                  name="tool-01"
                  size={20}
                  color={colors['Tertiary-Color']}
                  className="min-w-5"
                />
              ),
              iconActive: (
                <Icon
                  name="tool-01"
                  size={20}
                  color={colors['Primary-Color']}
                  className="min-w-5"
                />
              ),
            },
          ],
        },
      ],
      // apps: [
      //   {
      //     key: nanoid(),
      //     path: '/utilities/citemind',
      //     icon: (
      //       <Icon
      //         name="file-search-02"
      //         size={20}
      //         color={colors['Tertiary-Color']}
      //       />
      //     ),
      //     iconActive: (
      //       <Icon
      //         name="file-search-02"
      //         size={20}
      //         color={colors['Primary-Color']}
      //       />
      //     ),
      //     name: 'CiteMind',
      //     showInNewTab: true,
      //   },
      //   {
      //     key: nanoid(),
      //     path: '/extension-utilities',
      //     icon: (
      //       <Icon
      //         name="puzzle-piece-01"
      //         size={20}
      //         color={colors['Tertiary-Color']}
      //       />
      //     ),
      //     iconActive: (
      //       <Icon
      //         name="puzzle-piece-01"
      //         size={20}
      //         color={colors['Primary-Color']}
      //       />
      //     ),
      //     name: 'Extension Utilities',
      //   },
      // ],

      studio: [
        {
          path: [
            '/workflows/my-workflows',
            '/workflows/my-workflows/:workflowId',
            '/workflows/my-auto-processing-workflows',
            '/workflows/my-auto-processing-workflows/:workflowId',
          ],
          icon: (
            <Icon
              name="dataflow-03"
              size={20}
              color={colors['Tertiary-Color']}
              className="min-w-5"
            />
          ),
          iconActive: (
            <Icon
              name="dataflow-03"
              size={20}
              color={colors['Primary-Color']}
              className="min-w-5"
            />
          ),
          name: t('sidebar.my_workflows'),
        },

        {
          path: '/workers',
          icon: (
            <Icon
              name="face-id-square"
              size={20}
              color={colors['Tertiary-Color']}
              className="min-w-5"
            />
          ),
          iconActive: (
            <Icon
              name="face-id-square"
              size={20}
              color={colors['Primary-Color']}
              className="min-w-5"
            />
          ),
          name: t('sidebar.workers'),
        },
        {
          path: '/connections',
          icon: (
            <IconConnection
              color={colors['Tertiary-Color']}
              className="min-w-5"
            />
          ),
          iconActive: (
            <IconConnection
              color={colors['Primary-Color']}
              className="min-w-5"
            />
          ),
          name: t('sidebar.connections'),
        },
        {
          path: '/tools/tool-collection',
          icon: (
            <Icon
              name="tool-01"
              size={20}
              color={colors['Tertiary-Color']}
              className="min-w-5"
            />
          ),
          iconActive: (
            <Icon
              name="tool-01"
              size={20}
              color={colors['Primary-Color']}
              className="min-w-5"
            />
          ),
          name: t('sidebar.tool_collection'),
        },

        {
          path: '/tools/tool-category',
          icon: (
            <Icon
              name="layers-two-01"
              size={20}
              color={colors['Tertiary-Color']}
              className="min-w-5"
            />
          ),
          iconActive: (
            <Icon
              name="layers-two-01"
              size={20}
              color={colors['Primary-Color']}
              className="min-w-5"
            />
          ),
          name: t('sidebar.tool_category'),
          isHidden: !isSuperAdmin,
        },

        {
          path: '/ai-foundation',
          icon: (
            <Icon
              name="container"
              size={20}
              color={colors['Tertiary-Color']}
              className="min-w-5"
            />
          ),
          iconActive: (
            <Icon
              name="container"
              size={20}
              color={colors['Primary-Color']}
              className="min-w-5"
            />
          ),
          name: t('sidebar.ai_foundation'),
        },
      ],

      others: [
        {
          path: ['/knowledge-base', '/external-knowledge-base'],
          icon: (
            <Icon
              name="database-01"
              size={20}
              color={colors['Tertiary-Color']}
              className="min-w-5"
            />
          ),
          iconActive: (
            <Icon
              name="database-01"
              size={20}
              color={colors['Primary-Color']}
              className="min-w-5"
            />
          ),
          name: t('sidebar.knowledge_base'),
        },
      ],
    }

    return groups
  }, [isSuperAdmin, t])

  return (
    <div
      className={clsx(
        'sidebar relative flex h-full flex-col gap-4 rounded-3xl border border-border-base-icon bg-white pb-2 pt-3 transition-all',
        isExpanded
          ? 'w-[230px] min-w-[230px] max-w-[230px]'
          : 'w-[62px] min-w-[62px] max-w-[62px]'
      )}
    >
      <div
        className={clsx(
          'logo flex min-h-[40px] w-full cursor-pointer items-center overflow-hidden py-1',
          isExpanded
            ? 'justify-between pl-4 pr-3'
            : 'flex-col-reverse gap-2 px-3'
        )}
      >
        <Link to={rootUrls.Home}>
          <img
            src={
              isExpanded
                ? '/assets/images/genai-logo-v2-new.svg'
                : '/assets/images/genai-logo-small-v2.svg'
            }
            width={isExpanded ? 137 : 28}
          />
        </Link>
        <IconButton
          nameIcon={'chevron-left-double'}
          colorIcon={colors.neutral[300]}
          hoverColor={colors['Primary-Color']}
          onClick={() => setExpanded(!isExpanded)}
          className={isExpanded ? '' : 'rotate-180 transition-all'}
        />
      </div>

      {!isSuperAdmin && <OrgListInSidebar isExpanded={isExpanded} />}

      <div className="genai-scrollbar flex h-[calc(100%-100px)] flex-col gap-3 overflow-auto px-3">
        {Object.keys(menuGroupItems).length &&
          Object.keys(menuGroupItems).map((key, index) => {
            return (
              <GroupMenu
                key={index}
                title={key}
                menuItems={menuGroupItems[key]}
                isExpanded={isExpanded}
                index={index}
              />
            )
          })}
      </div>

      <div
        className={clsx(
          'flex w-full items-center border-t border-border-base-icon pt-2',
          isExpanded ? 'justify-between px-3' : 'justify-center px-0'
        )}
      >
        {isExpanded && (
          <div className="flex w-[calc(100%-28px)] items-center gap-2">
            <div className="flex items-center justify-center p-1">
              <Avatar
                className={'h-[40px] w-[40px] border-[2px] border-[#F5F6F7]'}
                avatarUrl={myProfile?.avatar || ''}
                name={myProfile?.first_name ?? myProfile?.username ?? ''}
              />
            </div>
            <div className="flex w-[calc(100%-56px)] flex-col">
              <Text ellipsis elementType="div" type="subBody" variant="medium">
                {myProfile?.first_name ?? myProfile?.username ?? ''}
              </Text>
              <Text
                ellipsis
                elementType="div"
                type="supportText"
                className="text-Secondary-Color"
              >
                {myProfile?.email ?? ''}
              </Text>
            </div>
          </div>
        )}

        <Popover
          open={isOpenAccSettings}
          overlayClassName={clsx(
            'z-20 flex flex-col !overflow-hidden rounded-[12px] border-[0.5px] border-border-base-icon bg-white !p-0 shadow-md',
            isExpanded ? 'ml-6' : 'ml-4'
          )}
          trigger="click"
          placement="right end"
          content={<SidebarPopoverContent />}
          onOpenChange={(value) => setIsOpenAccSettings(value)}
          isPure
        >
          {isExpanded ? (
            <div
              className={clsx(
                'flex items-center justify-center rounded-md p-1 hover:bg-Hover-2',
                isOpenAccSettings && 'bg-Hover-2'
              )}
            >
              <Icon
                name="chevron-up"
                size={20}
                color={colors['Primary-Color']}
                className="rotate-90"
              />
            </div>
          ) : (
            <div
              className={clsx(
                'flex items-center justify-center rounded-full p-1',
                isOpenAccSettings && 'bg-Hover-Color'
              )}
            >
              <Avatar
                className={'h-[40px] w-[40px] border-[2px] border-[#F5F6F7]'}
                avatarUrl={myProfile?.avatar || ''}
                name={myProfile?.first_name ?? myProfile?.username ?? ''}
              />
            </div>
          )}
        </Popover>
      </div>
    </div>
  )
}

export default memo(Sidebar)
