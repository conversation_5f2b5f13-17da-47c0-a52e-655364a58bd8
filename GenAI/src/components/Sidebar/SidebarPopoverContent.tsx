import { useAuth } from '@/hooks/useAuth'
import { rootUrls } from '@/routes/rootUrls'
import { memo } from 'react'
import { useNavigate } from 'react-router-dom'
import Text from '../Text'
import useSidebarStore from './SidebarStore'

import { useOrg } from '@/hooks/useOrg'
import { useTranslation } from 'react-i18next'
import '../Header/UserPopoverContent.scss'

const SidebarPopoverContent = () => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const { logout } = useAuth()
  const resetStore = useSidebarStore.use.resetStore()
  const { resetStore: resetOrgStore } = useOrg()

  const handleLogout = () => {
    new Promise((resolve) => {
      logout()
      resetStore()
      resetOrgStore()
      resolve('')
    }).then(() => navigate(rootUrls.Login, { replace: true }))
  }

  return (
    <div className="h-[166px] w-[176px] bg-white">
      <div className="flex flex-col items-center gap-[16px] p-2">
        <div
          onClick={() => {
            navigate(rootUrls.Profile)
          }}
          className="settings-header flex w-full cursor-pointer items-center gap-[8px] rounded-md px-[8px] py-[4px] hover:bg-Hover-Color"
        >
          <div className="icon-settings-header"></div>
          <Text type="subBody" className="text-Primary-Color">
            {t('common.settings')}
          </Text>
        </div>
        <div
          className="pricing-header flex w-full cursor-pointer items-center gap-[8px] rounded-md px-[8px] py-[4px] hover:bg-Hover-Color"
          onClick={() => {
            navigate(rootUrls.PlansPricing)
          }}
        >
          <div className="icon-pricing-header"></div>
          <Text type="subBody" className="text-Primary-Color">
            {t('sidebar.pricing')}
          </Text>
        </div>
      </div>

      <div className="h-[1px] w-full bg-neutral-100"></div>

      <div className="flex flex-col items-center gap-[16px] px-[8px] py-[8px]">
        <div
          onClick={() => handleLogout()}
          className="logout-header flex w-full cursor-pointer items-center gap-[8px] rounded-md px-[8px] py-[4px] hover:bg-Hover-Color"
        >
          <div className="icon-logout-header rotate-90"></div>
          <Text type="subBody" className="text-Primary-Color">
            {t('common.logout')}
          </Text>
        </div>

        <div className="flex w-full items-center justify-between p-[4px]">
          <Text
            type="supportText"
            variant="medium"
            className="text-Secondary-Color"
          >
            {t('sidebar.privacy')}
          </Text>
          <Text
            type="supportText"
            variant="medium"
            className="text-Secondary-Color"
          >
            {t('sidebar.terms')}
          </Text>
          <Text
            type="supportText"
            variant="medium"
            className="text-Secondary-Color"
          >
            {t('sidebar.copyright')}
          </Text>
        </div>
      </div>
    </div>
  )
}

export default memo(SidebarPopoverContent)
