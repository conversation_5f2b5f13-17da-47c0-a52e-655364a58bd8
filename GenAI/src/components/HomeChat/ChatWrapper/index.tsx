import {
  WorkflowPub<PERSON><PERSON>ist,
  WorkflowResponseMessage,
  citationGetPerplexityCompletionHome,
  usersReadCurrentUser,
  workflowReadWorkflowByIdApi,
  workflowUploadFileWorkflow,
} from '@/apis/client'
import { findNodeById } from '@/components/PlaygroundMultiAgent/helper'
import { GEN_AI_INTERNAL_PATH, HTTP_STATUS_CODE } from '@/constants'
import {
  cn,
  getAccessTokenLocalStorage,
  getSessionIdFromWsUrlHomeChat,
  getWsWorkflowUrlHome,
} from '@/helpers'
import {
  EMessageType,
  IMessage,
  getTimeChat,
} from '@/pages/Workers/components/ModalUpdateWorker/WorkerSetting/Playground/helpers'
import { NODE_TYPE } from '@/pages/WorkflowDetail/constants'
import { CustomNode } from '@/pages/WorkflowDetail/types'
import moment from 'moment'
import { nanoid } from 'nanoid'
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { v4 as uuid } from 'uuid'
import ActionBar, { EActionBar } from '../components/ActionBar'
import ChatHeader from '../components/ChatHeader'
import SelectWorkflow from '../components/SelectWorkflow/SelectWorkflow'
import MessageInputSkeleton from '../components/Skeleton/MessageInputSkeleton'
import {
  DEFAULT_HOME_CHAT_WORKFLOW_ID,
  DEFAULT_SEND_FILE_TITLE,
  MAXIMUM_RECONNECT_WS,
} from '../const'
import useChatStore, {
  FIELD_PRESERVE_FOR_NEW_CHAT,
} from '../store/useChatStore'
import { ILocalMessageParams } from './helper'
import SendMessageWrapper from './SendMessageWrapper'

import { useLatestValue } from '@/hooks/useLatestValue'
import MessagesList from './MessagesList'
import ListCategories from '../components/ListCategories'
import ContinueLastConversation from './ContinueLastConversation'

const ChatWrapper = () => {
  const textAreaRef = useRef<any>()

  const [responding, setResponding] = useState(false)
  const [responseMessage, setResponseMessage] = useState('')
  const [responseTime, setResponseTime] = useState('')
  const [hasRelatedQuestion, setHasRelatedQuestion] = useState(false)

  const socket = useRef<WebSocket | null>(null)

  const mounted = useRef(true)

  const [isAttachingFile, setAttachingFile] = useState(false)

  const [wsMessage, setWsMessage] = useState<WorkflowResponseMessage | null>(
    null
  )

  const [isWebSearchOn, setWebSearchMode] = useState(false)

  const messageQueue = useRef<WorkflowResponseMessage[]>([])

  const [isLoadingWorkflow, setIsLoadingWorkflow] = useState(true)

  const reconnectCounter = useRef(MAXIMUM_RECONNECT_WS)

  const {
    resetStore,
    isChatHistoryOpen,
    setIsChatHistoryOpen,
    isChatAtBottom,
    setChatAtBottom,
    currentWorkflowData,
    setCurrentWorkflowData,
    currentWorkflow,
    setCurrentWorkflow,
    message,
    setMessage,
    text,
    setText,
    files,
    chatTitle,
    setChatTitle,
    unshiftHistory,
    // use to check reconnect to playground
    sessionId,
    setSessionId,
    isLoadingPrevMessages,
    setActiveHistory,
    activeHistory,
    moveHistoryToTop,
    addWorkflowData,
    isDisableChat,
    setIsDisableChat,
  } = useChatStore()

  const lastSessionIdRef = useLatestValue(sessionId)

  const latestConnectingWorkflowId = useRef<string>()
  const workflowId = currentWorkflow?.id

  const nodes = useMemo(
    () =>
      currentWorkflowData?.config.nodes.map((node) => {
        if (node.type === NODE_TYPE.START || node.type === NODE_TYPE.END)
          return {
            ...node,
            deletable: false,
          }

        return node
      }) as CustomNode[],
    [currentWorkflowData]
  )

  const isProcessing = useRef(false)
  const processQueue = () => {
    if (messageQueue.current.length > 0 && !isProcessing.current) {
      isProcessing.current = true
      const message = messageQueue.current.shift()
      setWsMessage(message!)
      setTimeout(() => {
        isProcessing.current = false
        processQueue()
      }, 10)
    }
  }

  // Load workflow data
  useEffect(() => {
    const getData = async () => {
      if (!workflowId) return

      const res = await workflowReadWorkflowByIdApi({
        path: { workflow_id: workflowId },
      })

      if (res.status === HTTP_STATUS_CODE.SUCCESS && res.data) {
        const { data } = res

        setCurrentWorkflowData(data.data)
        setCurrentWorkflow({
          id: data.data.id,
          name: data.data.name,
        })
        addWorkflowData(data.data)
      } else {
        setIsDisableChat(true)
      }

      setIsLoadingWorkflow(false)
    }

    getData()
  }, [workflowId])

  const connectToPlayground = (callbackFunc?: any) => {
    const token = getAccessTokenLocalStorage()
    if (socket.current) {
      socket.current.close()
      socket.current = null
    }
    setResponding(false)
    setResponseMessage('')
    if (!workflowId) return

    latestConnectingWorkflowId.current = workflowId

    const tSocket = new WebSocket(
      getWsWorkflowUrlHome(token ?? '', workflowId, sessionId)
    )
    tSocket.onopen = () => {
      console.log('Connected to playground')

      if (callbackFunc) callbackFunc()
    }
    tSocket.onclose = (e) => {
      console.log('Onclose to playground', e)
      if (e.code === 3401) {
        usersReadCurrentUser()
      }

      const prevSessionId = getSessionIdFromWsUrlHomeChat(
        (e.currentTarget as WebSocket)?.url
      )

      if (
        mounted.current &&
        prevSessionId &&
        lastSessionIdRef?.current === prevSessionId &&
        latestConnectingWorkflowId.current === workflowId &&
        reconnectCounter.current > 0
      ) {
        setTimeout(() => {
          console.log('Reconnect to playground')
          connectToPlayground()
        }, 2000)
        reconnectCounter.current = reconnectCounter.current - 1

        // socket.current = null
      }
    }
    socket.current = tSocket
  }

  useEffect(() => {
    if (!socket.current) return

    socket.current.onmessage = (event) => {
      const res = JSON.parse(event.data) as WorkflowResponseMessage
      const node = findNodeById(nodes, res.speaker_id ?? '')

      switch (res.type) {
        case EMessageType.worker:
          if (!responseTime) {
            setResponseTime(getTimeChat())
          }
          if (res.related_question) setHasRelatedQuestion(true)
          setResponseMessage((prev) => prev + res.text)

          break

        case EMessageType.end:
          setResponding(false)

          if (responseMessage) {
            setMessage((pre) => [
              {
                id: nanoid(),
                text: responseMessage,
                type: EMessageType.worker,
                time: responseTime,
                worker: {
                  workerName: node?.data.workerName ?? '',
                  workerAvatar: node?.data.workerAvatar,
                },
                related_question: hasRelatedQuestion,
              },
              ...pre,
            ])
            setResponseMessage('')
            setResponseTime('')
            setHasRelatedQuestion(false)
          }
          break
        default:
          messageQueue.current.push(res)
          processQueue()
          break
      }
    }
    // socket.current.onclose = (e) => {
    //   console.log('Onclose to playground', e)
    //   if (e.code === 3401) {
    //     usersReadCurrentUser()
    //   }

    //   if (e.code === 1000) {
    //     reconnectCounter.current = MAXIMUM_RECONNECT_WS
    //   }

    //   const prevSessionId = getSessionIdFromWsUrlHomeChat(
    //     (e.currentTarget as WebSocket)?.url
    //   )

    //   if (
    //     mounted.current &&
    //     sessionId &&
    //     sessionId === prevSessionId &&
    //     latestConnectingWorkflowId.current === workflowId &&
    //     reconnectCounter.current > 0
    //   ) {
    //     setTimeout(() => {
    //       console.log('Reconnect to playground')
    //       connectToPlayground()
    //     }, 2000)

    //     reconnectCounter.current = reconnectCounter.current - 1

    //     // socket.current = null
    //   }
    // }
  }, [
    message,
    responseMessage,
    sessionId,
    workflowId,
    hasRelatedQuestion,
    responseTime,
  ])

  useEffect(() => {
    if (!wsMessage || !nodes) return

    const node = findNodeById(nodes, wsMessage.speaker_id ?? '')

    const isError = wsMessage.type === EMessageType.noTokenMessage
    if (isError && workflowId) {
      // call to check if workflow is still valid
      workflowReadWorkflowByIdApi({
        path: { workflow_id: workflowId },
      }).then((res) => {
        if (res.status !== HTTP_STATUS_CODE.SUCCESS) {
          setIsDisableChat(true)
        }
      })
    }

    switch (wsMessage.type) {
      // case EMessageType.workflowProgressMessage:
      //   if (node) {
      //     setCurrentWorkflowNode(node)
      //   }
      //   break

      // case EMessageType.worker:
      //   if (wsMessage.related_question) setResponding(false)

      //   setTimeout(() => {
      //     setMessage((pre) => [
      //       {
      //         id: nanoid(),
      //         text: wsMessage.text,
      //         type: EMessageType.worker,
      //         time: getTimeChat(),
      //         worker: {
      //           workerName: node?.data.workerName ?? '',
      //           workerAvatar: node?.data.workerAvatar,
      //         },
      //         related_question: wsMessage.related_question,
      //       },
      //       ...pre,
      //     ])
      //   }, 100)
      //   break

      // case EMessageType.end:
      //   setResponding(false)
      //   break

      case EMessageType.noTokenMessage:
        setMessage((pre) => [
          {
            id: nanoid(),
            text: wsMessage.text,
            type: EMessageType.noTokenMessage,
            time: getTimeChat(),
          },
          ...pre,
        ])
        setResponding(false)
        break

      case EMessageType.citationMessage:
        if (wsMessage.related_question) setResponding(false)

        // delay 100ms to avoid render message not in order
        setTimeout(() => {
          setMessage((pre) => [
            {
              id: nanoid(),
              text: wsMessage.text,
              type: EMessageType.citationMessage,
              time: getTimeChat(),
              citation: wsMessage.citation as any,
              speakerId: wsMessage.speaker_id!,
              worker: {
                workerName: node?.data.workerName ?? '',
                workerAvatar: node?.data.workerAvatar,
              },
              related_question: wsMessage.related_question,
            },
            ...pre,
          ])
        }, 100)

        break

      case EMessageType.fileUrlCitation:
        setMessage((pre) => [
          {
            id: nanoid(),
            text: wsMessage.text,
            type: EMessageType.fileUrlCitation,
            time: getTimeChat(),
            file_url: wsMessage.file_url!,
            worker: {
              workerName: node?.data.workerName ?? '',
              workerAvatar: node?.data.workerAvatar,
            },
          },
          ...pre,
        ])
        break

      default:
        break
    }
  }, [wsMessage])

  useEffect(() => {
    connectToPlayground()
    setMessage([])
    reconnectCounter.current = MAXIMUM_RECONNECT_WS
  }, [workflowId, sessionId])

  useEffect(() => {
    return () => {
      socket.current?.close()
      mounted.current = false
      resetStore()
    }
  }, [])

  const _setText = useCallback((value: string) => {
    setText(value)
    textAreaRef.current?.focus()
  }, [])

  const handleChatPerplexity = async (value: string) => {
    try {
      if (responding) return

      const res = await citationGetPerplexityCompletionHome({
        body: {
          messages: [
            {
              content: value,
              role: 'user',
            },
          ],
          workflow_id: workflowId!,
          session_id: sessionId,
          current_message: value,
        },
      })

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        const { data }: any = res
        // setResponding(false)
        setTimeout(
          () =>
            setMessage((pre) => [
              {
                id: nanoid(),
                text: data?.choices?.[0]?.message?.content,
                type: EMessageType.perplexityMessage,
                time: getTimeChat(),
                citations: data?.citations,
                related_question: false,
              },
              ...pre,
            ]),
          100
        )
      }
    } catch (error) {
      console.log(error)
    } finally {
      setResponding(false)
    }
  }

  const chatHandler = (tVal: string) => {
    const val = tVal.trim()

    if (socket?.current?.readyState === socket?.current?.CLOSED) {
      connectToPlayground(() => chatHandler(tVal))

      return
    }

    if (
      val.length === 0 ||
      responding ||
      !workflowId ||
      socket?.current?.readyState !== socket?.current?.OPEN
    )
      return

    // update history local
    if (!chatTitle) {
      setChatTitle(val)
      const history = {
        id: uuid(),
        title: val,
        session_id: sessionId,
        content: val,
        workflow_id: workflowId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }
      unshiftHistory(history)
      setActiveHistory(history)
    } else if (activeHistory) {
      moveHistoryToTop(activeHistory)
    }

    setChatAtBottom(true)
    setResponding(true)
    setText('')
    setMessage((pre) => [
      {
        id: nanoid(),
        text: val,
        type: EMessageType.user,
        time: getTimeChat(),
      },
      ...pre,
    ])

    if (isWebSearchOn) {
      handleChatPerplexity(val)
    } else {
      socket.current?.send(
        JSON.stringify({
          text: val,
          type: EMessageType.user,
          // chat_history: message
          //   .map(({ text, type }) => ({
          //     text,
          //     type,
          //   }))
          //   .reverse(),
        })
      )
    }
  }

  const handleFiles = async (
    __type: EMessageType,
    params: ILocalMessageParams
  ) => {
    try {
      if (isAttachingFile || !params.files || !sessionId || !workflowId) return

      const { text, intent } = params

      if (!chatTitle) {
        setChatTitle(intent ?? text ?? DEFAULT_SEND_FILE_TITLE)
        const history = {
          id: uuid(),
          title: intent ?? text ?? DEFAULT_SEND_FILE_TITLE,
          session_id: sessionId,
          content: responseMessage,
          workflow_id: workflowId!,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }
        unshiftHistory(history)
        setActiveHistory(history)
      } else if (activeHistory) {
        moveHistoryToTop(activeHistory)
      }

      if (text) {
        setText('')
      }
      setChatAtBottom(true)
      setResponding(true)

      const files = [...params.files]

      if (files?.length) {
        // Fake file message
        let listFakeMessageFiles = files

        const idMessage = nanoid()
        const time = getTimeChat()

        setAttachingFile(true)

        setMessage((pre) => [
          {
            id: idMessage,
            files,
            text,
            intent,
            type: EMessageType.fileUploadMessage,
            time,
          },
          ...pre,
        ])

        const listFilePromises = files.map((file) => {
          return workflowUploadFileWorkflow({
            baseURL: GEN_AI_INTERNAL_PATH,
            body: {
              file_display_name: file?.name,
              file_storage_upload: file.file,
              file_intent: (intent as any) || null,
              file_message: text,
            },
            path: {
              workflow_id: workflowId,
              session_id: sessionId,
            },
          })
        })

        const listFileResponses = await Promise.all(listFilePromises)

        const fileSuccess: any = []

        for (let i = 0; i < listFileResponses?.length; i++) {
          if (listFileResponses[i].status === HTTP_STATUS_CODE.SUCCESS) {
            const dataRes = listFileResponses[i].data

            if (dataRes)
              fileSuccess.push({
                file_id: dataRes.data.id,
                file_name: files[i].name,
                file_size: files[i].size,
                file_type: files[i].type,
                session_id: sessionId,
                id: dataRes.data.id,
                file_extension: /(?:\.([^.]+))?$/
                  .exec(files[i].name)?.[1]
                  ?.toLowerCase(),
                created_at: moment().utc().toString(),
              })
          } else {
            listFakeMessageFiles = listFakeMessageFiles?.map(
              (item: any, index: number) => {
                if (i === index && !item.error) {
                  return {
                    ...item,
                    error: 'Failed',
                  }
                }
                return item
              }
            )
          }
        }

        const newMessage = {
          id: idMessage,
          files: listFakeMessageFiles,
          text,
          intent,
          type: EMessageType.fileUploadMessage,
          time,
        }

        setMessage((prev) => {
          const newMessages = JSON.parse(JSON.stringify(prev)) as IMessage[]

          // update message with idMessage
          const index = newMessages.findIndex((item) => item.id === idMessage)
          if (index !== -1) {
            newMessages[index] = newMessage
          }

          return newMessages
        })

        if (
          fileSuccess?.length &&
          sessionId === getSessionIdFromWsUrlHomeChat(socket?.current?.url)
        ) {
          socket?.current?.send(
            JSON.stringify({
              type: EMessageType.fileUploadMessage,
              text: fileSuccess?.map((item: any) => item.file_id)?.join(', '),
            })
          )

          setTimeout(() => {
            socket?.current?.send(
              JSON.stringify({
                type: EMessageType.user,
                text: intent ?? text,
                file_upload: fileSuccess?.map((item: any) => item),
              })
            )
          }, 100)
        } else {
          setResponding(false)
        }
      }
    } catch (error) {
      console.log('error', error)
    } finally {
      setAttachingFile(false)
    }
  }

  const handleCreateNewChat = () => {
    resetStore(FIELD_PRESERVE_FOR_NEW_CHAT)

    if (!currentWorkflow) {
      setCurrentWorkflow({
        id: DEFAULT_HOME_CHAT_WORKFLOW_ID,
      })
    }
    setSessionId(uuid())
  }

  const handleClickWfItem = (workflow: WorkflowPublicList) => {
    resetStore(FIELD_PRESERVE_FOR_NEW_CHAT)
    setSessionId(uuid())

    setCurrentWorkflow({
      id: workflow.id,
      name: workflow.name,
    })
  }

  const handleClickActionBar = (action: EActionBar) => {
    if (action === EActionBar.HISTORY) {
      setIsChatHistoryOpen(!isChatHistoryOpen)
    } else if (action === EActionBar.NEW_CHAT) {
      if (!message.length) return
      handleCreateNewChat()
    }
  }

  return (
    <div
      className={cn(
        'flex h-full w-[807px] flex-col items-center justify-center overflow-hidden',
        {
          'w-[770px]': files!.length > 0,
        }
      )}
    >
      <div
        className={cn(
          'relative h-0 w-full overflow-hidden transition-all duration-100',
          {
            'flex h-full': isChatAtBottom,
          }
        )}
      >
        <MessagesList
          responding={responding}
          _setText={_setText}
          responseMessage={responseMessage}
        />
      </div>
      <ContinueLastConversation />

      <div className="flex w-full flex-col gap-[32px]">
        {!isChatAtBottom && <ChatHeader />}

        <div className={cn('relative flex w-full flex-col gap-[8px]')}>
          <div
            className={cn('flex h-[32px] items-center gap-[4px] px-[4px]', {
              'absolute top-[-40px]': isChatAtBottom,
            })}
          >
            <SelectWorkflow onClickItem={handleClickWfItem} />
          </div>

          {isLoadingWorkflow ? (
            <MessageInputSkeleton />
          ) : (
            <div className="w-full p-[2px]">
              <SendMessageWrapper
                ref={textAreaRef}
                responding={responding || Boolean(isLoadingPrevMessages)}
                text={text}
                setText={setText}
                onSend={chatHandler}
                handleFiles={handleFiles}
                isAttachingFile={isAttachingFile}
                isWebSearchOn={isWebSearchOn}
                setWebSearchMode={setWebSearchMode}
                isChatAtBottom={isChatAtBottom}
                currentWorkflow={currentWorkflow}
                isDisableChat={isDisableChat}
              />
            </div>
          )}

          {!isChatAtBottom && <ListCategories />}
        </div>
      </div>

      <ActionBar
        isChatHistoryOpen={isChatHistoryOpen}
        onClick={handleClickActionBar}
      />
    </div>
  )
}

export default memo(ChatWrapper)
