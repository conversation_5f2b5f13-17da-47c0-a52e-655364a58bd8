import Checkbox from '@/components/Checkbox'
import Input from '@/components/Input'
import Message from '@/components/Message'
import Text from '@/components/Text'
import { Dispatch, SetStateAction, memo, useCallback, useState } from 'react'
import { useTranslation } from 'react-i18next'
import SimpleBar from 'simplebar-react'

const DEFAULT_GROUPS = [
  'Marketing',
  'Research & Development',
  'Sales',
  'Finance',
  'Human Resources',
]

interface IProps {
  groupSelected?: string[]
  setGroupSelected?: Dispatch<SetStateAction<string[] | undefined>>
  setDirtyModal?: Dispatch<SetStateAction<boolean>>
}

const AddGroup = ({
  groupSelected,
  setGroupSelected,
  setDirtyModal,
}: IProps) => {
  const { t } = useTranslation()
  const [listGroups, setListGroups] = useState<string[]>(
    DEFAULT_GROUPS?.map((item) => t(item))
  )

  const [newGroup, setNewGroup] = useState<string>('')

  const handleToggleGroup = useCallback((group: string) => {
    setGroupSelected?.((prev) => {
      if (prev?.includes(group)) {
        return prev.filter((item) => item !== group)
      } else {
        return [...(prev || []), group]
      }
    })
    setDirtyModal?.(true)
  }, [])

  const handleAddGroup = useCallback(() => {
    if (newGroup?.trim()) {
      if (listGroups.includes(newGroup.trim())) {
        Message.error({ message: t('org.group_exists_error') })

        return
      }
      setListGroups((prev) => [newGroup, ...prev])
      handleToggleGroup(newGroup.trim())
      setNewGroup('')
      setDirtyModal?.(true)
    }
  }, [t, listGroups, newGroup])

  return (
    <div className="flex flex-col gap-2">
      <Input
        label={t('org.member_group')}
        placeholder={t('org.add_group_placeholder')}
        value={newGroup}
        onChange={(e) => {
          setNewGroup(e.target.value)
          setDirtyModal?.(true)
        }}
        onBlur={(e) => {
          setNewGroup(e.target.value?.trim())
        }}
        onPressEnter={handleAddGroup}
        className="w-full"
        maxLength={50}
      />
      <SimpleBar className="max-h-[160px]">
        <div className="flex flex-wrap gap-3 p-1">
          {listGroups?.map((item) => (
            <div
              key={item}
              className="flex cursor-pointer items-center gap-1.5 rounded-lg border border-border-base-icon bg-white py-2 pl-2 pr-3 hover:bg-Hover-Color"
              onClick={() => handleToggleGroup(item)}
            >
              <Text className="text-Tertiary-Color" type="subBody">
                {item}
              </Text>
              <Checkbox
                style="mono"
                iconSize={14}
                defaultChecked={groupSelected?.includes(item)}
                className="!h-[16px] !w-[16px] !p-[1.33px]"
              />
            </div>
          ))}
        </div>
      </SimpleBar>
      {!!groupSelected?.length && (
        <div className="flex w-full justify-end">
          <div
            className="flex cursor-pointer items-center justify-center rounded-md p-1 hover:bg-neutral-100"
            onClick={() => {
              setGroupSelected?.(undefined)
              setDirtyModal?.(true)
            }}
          >
            <Text type="subBody" className="text-Tertiary-Color">
              {t('common.clear_all')}
            </Text>
          </div>
        </div>
      )}
    </div>
  )
}

export default memo(AddGroup)
