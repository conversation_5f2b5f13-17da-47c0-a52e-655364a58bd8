import BaseModal from '@/components/BaseModal'
import { MessageDialog } from '@/components/DialogMessage'
import Input from '@/components/Input'
import Text from '@/components/Text'
import Upload from '@/components/Upload'
import { useOrg } from '@/hooks/useOrg'
import { memo, useCallback, useState } from 'react'
import { useTranslation } from 'react-i18next'
import AddGroup from './AddGroup'

interface IModalCreateOrgProps {
  isOpen: boolean
  onClose: () => void
}

const ModalCreateOrg = ({ isOpen, onClose }: IModalCreateOrgProps) => {
  const { t } = useTranslation()

  const [logo_file, setLogoFile] = useState<File>()
  const [logo, setLogo] = useState<string>()
  const [orgName, setOrgName] = useState<string>('')
  const [groupSelected, setGroupSelected] = useState<string[]>()

  const [isCreating, setCreating] = useState<boolean>(false)

  const [isDirtyModal, setDirtyModal] = useState<boolean>(false)

  const { createOrg } = useOrg()

  const handleCloseModal = useCallback(() => {
    if (isDirtyModal) {
      MessageDialog.warning({
        mainMessage: t('dialog.wanna_leave.title'),
        subMessage: t('dialog.wanna_leave.description'),
        onClick: () => {
          onClose()
          setDirtyModal(false)
        },
      })
    } else {
      onClose()
      setDirtyModal(false)
    }
  }, [onClose, isDirtyModal, t])

  const handleCreateOrg = useCallback(() => {
    if (isCreating || !orgName?.trim()) return
    setCreating(true)
    createOrg(
      {
        name: orgName.trim(),
        logo_file,
        groupSelected,
      },
      () => {
        setCreating(false)
        onClose()
      },
      () => {
        setCreating(false)
      }
    )
  }, [orgName, logo_file, groupSelected, createOrg, t, isCreating])

  return (
    <BaseModal
      title={t('org.create_new_org')}
      subTitle={t('org.create_org_modal.subtitle')}
      isOpen={isOpen}
      isShowCloseButton
      hasCancelButton={false}
      agreeLabel={t('org.create_org_modal.create_btn')}
      isInvalid={!orgName?.trim()}
      onClose={handleCloseModal}
      onAgree={handleCreateOrg}
      isLoading={isCreating}
    >
      <div className="flex w-[695px] flex-col gap-2 rounded-xl border border-neutral-200 bg-white p-8 pt-4">
        <div className="flex flex-col gap-1">
          <Text className="text-Tertiary-Color" type="subBody" variant="medium">
            {t('org.logo')}
          </Text>
          <Upload
            onChangeFile={(file: File | null) => {
              if (file) setLogoFile(file)
            }}
            image={logo}
            type="mini"
            onChange={(image) => {
              setLogo(image)
              setDirtyModal(true)
            }}
            dataTypes="image/png, image/jpeg, image/jpg"
            size="large"
          />
        </div>

        <Input
          label={t('org.org_name')}
          placeholder={t('org.org_name_placeholder')}
          onChange={(e) => {
            setOrgName(e.target.value)
            setDirtyModal(true)
          }}
          className="w-full"
          required
          onBlur={(e) => {
            setOrgName(e.target.value?.trim())
          }}
          maxLength={50}
          value={orgName}
        />

        <AddGroup
          groupSelected={groupSelected}
          setGroupSelected={setGroupSelected}
          setDirtyModal={setDirtyModal}
        />
      </div>
    </BaseModal>
  )
}

export default memo(ModalCreateOrg)
