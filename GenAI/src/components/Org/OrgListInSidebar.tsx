import Icon from '@/assets/icon/Icon'
import { MAX_ORG_CAN_JOIN } from '@/constants'
import { useMyProfile } from '@/hooks/useMyProfile'
import { useOrg } from '@/hooks/useOrg'
import { rootUrls } from '@/routes/rootUrls'
import { colors } from '@/theme'
import clsx from 'clsx'
import { isEmpty } from 'lodash'
import { memo, useCallback, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router-dom'
import Avatar from '../Avatar'
import IconButton from '../IconButton'
import Popover from '../Popover'
import Text from '../Text'
import ModalCreateOrg from './ModalCreateOrg'

interface IProps {
  isExpanded: boolean
}

const OrgListInSidebar = ({ isExpanded }: IProps) => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const { listOrgs, currentOrg, switchOrg, leaveOrg } = useOrg()
  const { myProfile, isSuperAdmin } = useMyProfile()

  const [isOpenPopover, toggleOpenPopover] = useState(false)

  const [isOpenModalCreateOrg, toggleOpenModalCreateOrg] = useState(false)

  const renderOrgList = useCallback(() => {
    return (
      <div className="flex w-[205px] flex-col rounded-xl border border-border-base-icon bg-white p-2 shadow-base">
        <div
          className={clsx(
            'flex h-[36px] cursor-pointer items-center gap-1 rounded-lg p-2 hover:bg-Hover-Color'
          )}
          onClick={() => switchOrg(undefined)}
        >
          <Text>{t('org.my_space')}</Text>
          {isEmpty(currentOrg) && (
            <Icon name="check" size={20} gradient={['#642B73', '#C6426E']} />
          )}
        </div>
        {!!listOrgs?.length && (
          <div className="flex w-full flex-col">
            <Text
              type="supportText"
              className="mb-1 px-1.5 text-Tertiary-Color"
            >
              {t('org.org_space')}
            </Text>

            {listOrgs?.map((org) => (
              <div
                key={org.id}
                className={clsx(
                  'group flex w-full cursor-pointer items-center gap-2 rounded-lg px-2 py-1 hover:bg-Hover-Color'
                )}
                onClick={() => switchOrg(org)}
              >
                <Avatar
                  hasBorder
                  variant="square"
                  size="smaller"
                  avatarUrl={org.avatar || ''}
                  name={org.name}
                  avatarDefault={
                    <Icon
                      name="building-07"
                      size={20}
                      color={colors['Primary-Color']}
                    />
                  }
                />
                <div className="flex w-fit flex-col overflow-hidden">
                  <Text ellipsis elementType="div" variant="medium">
                    {org.name}
                  </Text>
                  {org?.description && (
                    <Text
                      ellipsis
                      elementType="div"
                      className="text-Tertiary-Color"
                      type="supportText"
                    >
                      {org.description}
                    </Text>
                  )}
                </div>
                {currentOrg?.id === org.id && (
                  <Icon
                    name="check"
                    size={20}
                    gradient={['#642B73', '#C6426E']}
                  />
                )}

                <div className="ml-auto hidden items-center group-hover:flex">
                  <IconButton
                    nameIcon="leave-org"
                    sizeIcon={24}
                    colorIcon={colors['Tertiary-Color']}
                    hoverColor={colors['Primary-Color']}
                    tooltipText={t('org.leave_org')}
                    onClick={(e) => {
                      leaveOrg(org, () => navigate(rootUrls.Home))
                      e.stopPropagation()
                    }}
                  />
                </div>
              </div>
            ))}
          </div>
        )}
        {listOrgs?.length < MAX_ORG_CAN_JOIN && !isSuperAdmin ? (
          <div
            className="flex cursor-pointer items-center gap-1 rounded-md p-1 hover:bg-neutral-100"
            onClick={() => toggleOpenModalCreateOrg(true)}
          >
            <Icon name="plus-02" size={16} color={colors['Tertiary-Color']} />
            <Text type="subBody" className="text-Tertiary-Color">
              {t('org.create_new_org')}
            </Text>
          </div>
        ) : null}
      </div>
    )
  }, [t, listOrgs, currentOrg, isSuperAdmin])

  return (
    <>
      <Popover
        open={isOpenPopover}
        overlayClassName={clsx(
          'flex flex-col !overflow-hidden rounded-[12px] border-[0.5px] border-border-base-icon bg-white !p-0 shadow-md'
        )}
        trigger="click"
        content={renderOrgList()}
        onOpenChange={(value) => toggleOpenPopover(value)}
        placement={isExpanded ? 'bottom' : 'right start'}
        isPure
      >
        {isExpanded ? (
          <div
            className={clsx(
              'flex h-[32px] w-full items-center justify-between rounded-md py-1 pl-4 pr-3 hover:bg-Hover-Color',
              isOpenPopover && 'bg-Hover-Color'
            )}
          >
            <div className="flex max-w-[calc(100%-32px)] items-center gap-1.5">
              {isEmpty(currentOrg) ? (
                <>
                  <Icon
                    name="building-03"
                    size={16}
                    color={colors['Primary-Color']}
                  />

                  <Text
                    ellipsis
                    elementType="div"
                    type="subBody"
                    variant="medium"
                  >
                    {t('org.user_space', {
                      userName:
                        myProfile?.first_name ?? myProfile?.username ?? '',
                    })}
                  </Text>
                </>
              ) : (
                <>
                  <Avatar
                    hasBorder
                    variant="square"
                    size="smaller"
                    avatarUrl={currentOrg?.avatar || ''}
                    name={currentOrg?.name ?? ''}
                    avatarDefault={
                      <Icon
                        name="building-07"
                        size={16}
                        color={colors['Primary-Color']}
                      />
                    }
                  />
                  <Text
                    ellipsis
                    elementType="div"
                    type="subBody"
                    variant="medium"
                  >
                    {currentOrg?.name}
                  </Text>
                </>
              )}
            </div>

            <Icon
              name="chevron-selector-vertical"
              size={16}
              color={
                isOpenPopover ? colors['Primary-Color'] : colors.neutral['400']
              }
            />
          </div>
        ) : (
          <div className={clsx('flex h-[24px] items-center justify-center')}>
            {isEmpty(currentOrg) ? (
              <Icon
                name="building-03"
                size={20}
                color={colors['Primary-Color']}
              />
            ) : (
              <Avatar
                hasBorder
                variant="square"
                size="smaller"
                avatarUrl={currentOrg?.avatar || ''}
                name={currentOrg?.name ?? ''}
                avatarDefault={
                  <Icon
                    name="building-07"
                    size={20}
                    color={colors['Primary-Color']}
                  />
                }
              />
            )}
          </div>
        )}
      </Popover>

      {isOpenModalCreateOrg && (
        <ModalCreateOrg
          isOpen={isOpenModalCreateOrg}
          onClose={() => toggleOpenModalCreateOrg(false)}
        />
      )}
    </>
  )
}

export default memo(OrgListInSidebar)
