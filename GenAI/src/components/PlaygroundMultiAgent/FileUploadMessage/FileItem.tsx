/* eslint-disable max-len */
import Text from '@/components/Text'
import { cn, formatBytes } from '@/helpers'
import { FAILED_UPLOAD_FILE } from '@/pages/WorkflowDetail/components/PlaygroundAutomationProcess/helper'
import clsx from 'clsx'
import { memo } from 'react'

const FileItem = ({ file, className }: { file: any; className?: string }) => {
  const renderThumbFile = () => {
    if (file?.error && file?.error !== FAILED_UPLOAD_FILE) {
      return (
        <div className="flex size-7 items-center justify-center rounded-[6px] bg-red-50">
          <svg
            width="24"
            height="25"
            viewBox="0 0 24 25"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M21.7605 16.42L15.3605 4.9C14.5005 3.35 13.3105 2.5 12.0005 2.5C10.6905 2.5 9.50047 3.35 8.64047 4.9L2.24047 16.42C1.43047 17.89 1.34047 19.3 1.99047 20.41C2.64047 21.52 3.92047 22.13 5.60047 22.13H18.4005C20.0805 22.13 21.3605 21.52 22.0105 20.41C22.6605 19.3 22.5705 17.88 21.7605 16.42ZM11.2505 9.5C11.2505 9.09 11.5905 8.75 12.0005 8.75C12.4105 8.75 12.7505 9.09 12.7505 9.5V14.5C12.7505 14.91 12.4105 15.25 12.0005 15.25C11.5905 15.25 11.2505 14.91 11.2505 14.5V9.5ZM12.7105 18.21C12.6605 18.25 12.6105 18.29 12.5605 18.33C12.5005 18.37 12.4405 18.4 12.3805 18.42C12.3205 18.45 12.2605 18.47 12.1905 18.48C12.1305 18.49 12.0605 18.5 12.0005 18.5C11.9405 18.5 11.8705 18.49 11.8005 18.48C11.7405 18.47 11.6805 18.45 11.6205 18.42C11.5605 18.4 11.5005 18.37 11.4405 18.33C11.3905 18.29 11.3405 18.25 11.2905 18.21C11.1105 18.02 11.0005 17.76 11.0005 17.5C11.0005 17.24 11.1105 16.98 11.2905 16.79C11.3405 16.75 11.3905 16.71 11.4405 16.67C11.5005 16.63 11.5605 16.6 11.6205 16.58C11.6805 16.55 11.7405 16.53 11.8005 16.52C11.9305 16.49 12.0705 16.49 12.1905 16.52C12.2605 16.53 12.3205 16.55 12.3805 16.58C12.4405 16.6 12.5005 16.63 12.5605 16.67C12.6105 16.71 12.6605 16.75 12.7105 16.79C12.8905 16.98 13.0005 17.24 13.0005 17.5C13.0005 17.76 12.8905 18.02 12.7105 18.21Z"
              fill="#B91C1C"
            />
          </svg>
        </div>
      )
    }
    switch (file.type) {
      case 'application/pdf':
        return (
          <div className="flex size-7 items-center justify-center rounded-[6px] bg-red-50">
            <svg
              width="28"
              height="29"
              viewBox="0 0 28 29"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0 6.5C0 3.18629 2.68629 0.5 6 0.5L22 0.5C25.3137 0.5 28 3.18629 28 6.5L28 22.5C28 25.8137 25.3137 28.5 22 28.5L6 28.5C2.68629 28.5 0 25.8137 0 22.5L0 6.5Z"
                fill="#FEF2F2"
              />
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M9.26284 16.66C9.11262 16.66 9.00503 16.6378 8.94007 16.5933C8.87917 16.5488 8.8406 16.4902 8.82436 16.4174C8.80812 16.3446 8.8 16.2698 8.8 16.193L8.8 12.8815C8.8 12.8046 8.80812 12.7318 8.82436 12.6631C8.8406 12.5903 8.8812 12.5317 8.94616 12.4872C9.01112 12.4427 9.11871 12.4205 9.26893 12.4205H10.5174C10.6879 12.4205 10.8584 12.4508 11.0289 12.5115C11.2035 12.5721 11.3639 12.6672 11.5101 12.7965C11.6603 12.9219 11.78 13.0776 11.8694 13.2636C11.9627 13.4496 12.0094 13.6679 12.0094 13.9186C12.0094 14.1652 11.9627 14.3815 11.8694 14.5675C11.78 14.7535 11.6603 14.9092 11.5101 15.0346C11.3639 15.1559 11.2035 15.2489 11.0289 15.3135C10.8544 15.3742 10.6818 15.4045 10.5113 15.4045H9.73177L9.73177 16.1991C9.73177 16.2759 9.72365 16.3507 9.70741 16.4235C9.69117 16.4922 9.65057 16.5488 9.58561 16.5933C9.52065 16.6378 9.41306 16.66 9.26284 16.66ZM10.5174 14.4826H9.73177V13.3424H10.5113C10.625 13.3424 10.7244 13.3667 10.8097 13.4152C10.899 13.4637 10.968 13.5304 11.0168 13.6153C11.0655 13.7002 11.0898 13.7993 11.0898 13.9125C11.0898 14.0459 11.0594 14.1551 10.9985 14.24C10.9417 14.3249 10.8686 14.3876 10.7793 14.428C10.694 14.4644 10.6067 14.4826 10.5174 14.4826Z"
                fill="white"
              />
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M6 8.42C6 7.35961 6.85961 6.5 7.92 6.5L20.08 6.5C21.1404 6.5 22 7.35961 22 8.42V20.58C22 21.6404 21.1404 22.5 20.08 22.5H7.92C6.85961 22.5 6 21.6404 6 20.58L6 8.42ZM9.26284 16.66C9.11262 16.66 9.00503 16.6378 8.94007 16.5933C8.87917 16.5488 8.8406 16.4902 8.82436 16.4174C8.80812 16.3446 8.8 16.2698 8.8 16.193L8.8 12.8815C8.8 12.8046 8.80812 12.7318 8.82436 12.6631C8.8406 12.5903 8.8812 12.5317 8.94616 12.4872C9.01112 12.4427 9.11871 12.4205 9.26893 12.4205H10.5174C10.6879 12.4205 10.8584 12.4508 11.0289 12.5115C11.2035 12.5721 11.3639 12.6672 11.5101 12.7965C11.6603 12.9219 11.78 13.0776 11.8694 13.2636C11.9627 13.4496 12.0094 13.6679 12.0094 13.9186C12.0094 14.1652 11.9627 14.3815 11.8694 14.5675C11.78 14.7535 11.6603 14.9092 11.5101 15.0346C11.3639 15.1559 11.2035 15.2489 11.0289 15.3135C10.8544 15.3742 10.6818 15.4045 10.5113 15.4045H9.73177L9.73177 16.1991C9.73177 16.2759 9.72365 16.3507 9.70741 16.4235C9.69117 16.4922 9.65057 16.5488 9.58561 16.5933C9.52065 16.6378 9.41306 16.66 9.26284 16.66ZM12.8601 16.66C12.718 16.66 12.6145 16.6378 12.5495 16.5933C12.4886 16.5488 12.448 16.4922 12.4277 16.4235C12.4074 16.3507 12.3973 16.2739 12.3973 16.193V12.8754C12.3973 12.7986 12.4054 12.7258 12.4216 12.657C12.4379 12.5843 12.4785 12.5256 12.5434 12.4812C12.6084 12.4367 12.716 12.4165 12.8662 12.4205H13.9624C14.2547 12.4205 14.5267 12.4731 14.7784 12.5782C15.0302 12.6833 15.2514 12.8309 15.4423 13.0209C15.6331 13.211 15.7813 13.4334 15.8868 13.6881C15.9924 13.9428 16.0452 14.2198 16.0452 14.519C16.0452 14.8223 15.9924 15.1053 15.8868 15.3681C15.7813 15.6269 15.6331 15.8533 15.4423 16.0474C15.2514 16.2375 15.0281 16.3871 14.7724 16.4962C14.5206 16.6054 14.2446 16.66 13.9441 16.66H12.8601ZM13.3229 15.732H13.9624C14.1816 15.732 14.3785 15.6815 14.5531 15.5804C14.7277 15.4753 14.8657 15.3338 14.9672 15.1559C15.0687 14.9779 15.1195 14.7738 15.1195 14.5433C15.1195 14.3128 15.0687 14.1066 14.9672 13.9246C14.8657 13.7427 14.7277 13.6012 14.5531 13.5001C14.3785 13.395 14.1796 13.3424 13.9563 13.3424H13.3229V15.732ZM16.983 16.66C16.8368 16.66 16.7312 16.6398 16.6663 16.5993C16.6013 16.5549 16.5607 16.4962 16.5445 16.4235C16.5323 16.3507 16.5262 16.2759 16.5262 16.1991V12.8815C16.5262 12.7035 16.5607 12.5822 16.6297 12.5175C16.6987 12.4529 16.8226 12.4205 17.0012 12.4205H18.6633C18.7444 12.4205 18.8196 12.4286 18.8886 12.4448C18.9576 12.4569 19.0124 12.4953 19.053 12.56C19.0977 12.6247 19.12 12.7339 19.12 12.8875C19.12 13.0331 19.0977 13.1382 19.053 13.2029C19.0083 13.2676 18.9495 13.308 18.8764 13.3242C18.8074 13.3363 18.7323 13.3424 18.6511 13.3424H17.4519V14.1248H18.7003C18.7815 14.1248 18.8526 14.1309 18.9135 14.143C18.9784 14.1551 19.0292 14.1895 19.0657 14.2461C19.1023 14.3027 19.1205 14.3977 19.1205 14.5312C19.1205 14.6605 19.1023 14.7535 19.0657 14.8101C19.0292 14.8627 18.9784 14.8951 18.9135 14.9072C18.8526 14.9193 18.7795 14.9254 18.6942 14.9254H17.4519V16.2051C17.4519 16.2819 17.4438 16.3567 17.4275 16.4295C17.4113 16.4983 17.3707 16.5549 17.3057 16.5993C17.2448 16.6398 17.1372 16.66 16.983 16.66Z"
                fill="#B91C1C"
              />
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M12.8601 16.66C12.718 16.66 12.6145 16.6378 12.5495 16.5933C12.4886 16.5488 12.448 16.4922 12.4277 16.4235C12.4074 16.3507 12.3973 16.2739 12.3973 16.193V12.8754C12.3973 12.7986 12.4054 12.7258 12.4216 12.657C12.4379 12.5843 12.4785 12.5256 12.5434 12.4812C12.6084 12.4367 12.716 12.4165 12.8662 12.4205H13.9624C14.2547 12.4205 14.5267 12.4731 14.7784 12.5782C15.0302 12.6833 15.2514 12.8309 15.4423 13.0209C15.6331 13.211 15.7813 13.4334 15.8868 13.6881C15.9924 13.9428 16.0452 14.2198 16.0452 14.519C16.0452 14.8223 15.9924 15.1053 15.8868 15.3681C15.7813 15.6269 15.6331 15.8533 15.4423 16.0474C15.2514 16.2375 15.0281 16.3871 14.7724 16.4962C14.5206 16.6054 14.2446 16.66 13.9441 16.66H12.8601ZM13.9624 15.732H13.3229V13.3424H13.9563C14.1796 13.3424 14.3785 13.395 14.5531 13.5001C14.7277 13.6012 14.8657 13.7427 14.9672 13.9246C15.0687 14.1066 15.1195 14.3128 15.1195 14.5433C15.1195 14.7738 15.0687 14.9779 14.9672 15.1559C14.8657 15.3338 14.7277 15.4753 14.5531 15.5804C14.3785 15.6815 14.1816 15.732 13.9624 15.732Z"
                fill="white"
              />
              <path
                d="M16.983 16.66C16.8368 16.66 16.7312 16.6398 16.6663 16.5993C16.6013 16.5549 16.5607 16.4962 16.5445 16.4235C16.5323 16.3507 16.5262 16.2759 16.5262 16.1991V12.8815C16.5262 12.7035 16.5607 12.5822 16.6297 12.5175C16.6987 12.4529 16.8226 12.4205 17.0012 12.4205H18.6633C18.7444 12.4205 18.8196 12.4286 18.8886 12.4448C18.9576 12.4569 19.0124 12.4953 19.053 12.56C19.0977 12.6247 19.12 12.7339 19.12 12.8875C19.12 13.0331 19.0977 13.1382 19.053 13.2029C19.0083 13.2676 18.9495 13.308 18.8764 13.3242C18.8074 13.3363 18.7323 13.3424 18.6511 13.3424H17.4519V14.1248H18.7003C18.7815 14.1248 18.8526 14.1309 18.9135 14.143C18.9784 14.1551 19.0292 14.1895 19.0657 14.2461C19.1023 14.3027 19.1205 14.3977 19.1205 14.5312C19.1205 14.6605 19.1023 14.7535 19.0657 14.8101C19.0292 14.8627 18.9784 14.8951 18.9135 14.9072C18.8526 14.9193 18.7795 14.9254 18.6942 14.9254H17.4519V16.2051C17.4519 16.2819 17.4438 16.3567 17.4275 16.4295C17.4113 16.4983 17.3707 16.5549 17.3057 16.5993C17.2448 16.6398 17.1372 16.66 16.983 16.66Z"
                fill="white"
              />
              <path
                d="M9.73177 14.4826H10.5174C10.6067 14.4826 10.694 14.4644 10.7793 14.428C10.8686 14.3876 10.9417 14.3249 10.9985 14.24C11.0594 14.1551 11.0898 14.0459 11.0898 13.9125C11.0898 13.7993 11.0655 13.7002 11.0168 13.6153C10.968 13.5304 10.899 13.4637 10.8097 13.4152C10.7244 13.3667 10.625 13.3424 10.5113 13.3424H9.73177V14.4826Z"
                fill="#B91C1C"
              />
            </svg>
          </div>
        )
      case 'text/csv':
      case 'application/vnd.ms-excel':
      case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
        return (
          <div className="flex size-7 items-center justify-center rounded-[6px] bg-green-50">
            <svg
              width="16"
              height="17"
              viewBox="0 0 16 17"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M1.92 0.5C0.859613 0.5 0 1.35961 0 2.42L0 14.58C0 15.6404 0.859613 16.5 1.92 16.5H14.08C15.1404 16.5 16 15.6404 16 14.58V2.42C16 1.35961 15.1404 0.5 14.08 0.5L1.92 0.5ZM10.1808 11.6146C10.2805 11.5794 10.3979 11.5032 10.5328 11.3858C10.7323 11.2157 10.8291 11.0632 10.8232 10.9282C10.8232 10.7933 10.7411 10.6349 10.5768 10.453L8.88343 8.47563L10.5768 6.50183C10.7411 6.32583 10.8232 6.16743 10.8232 6.02663C10.8291 5.88583 10.7323 5.73036 10.5328 5.56023C10.4037 5.43703 10.2893 5.35783 10.1896 5.32263C10.0899 5.28743 9.99306 5.2933 9.8992 5.34023C9.80534 5.38716 9.70266 5.4781 9.5912 5.61303L8.0116 7.45757L6.432 5.61303C6.32054 5.4781 6.21786 5.38716 6.124 5.34023C6.03014 5.2933 5.93334 5.28743 5.8336 5.32263C5.73387 5.35783 5.61947 5.43703 5.4904 5.56023C5.2968 5.73036 5.2 5.88583 5.2 6.02663C5.2 6.16743 5.28213 6.32583 5.4464 6.50183L7.13977 8.47563L5.4464 10.453C5.28213 10.6349 5.2 10.7933 5.2 10.9282C5.2 11.0632 5.2968 11.2157 5.4904 11.3858C5.62533 11.5032 5.74267 11.5794 5.8424 11.6146C5.94214 11.6498 6.03894 11.644 6.1328 11.597C6.22666 11.5501 6.3264 11.4621 6.432 11.333L8.0116 9.49184L9.5912 11.333C9.70266 11.4621 9.8024 11.5501 9.8904 11.597C9.98426 11.644 10.0811 11.6498 10.1808 11.6146Z"
                fill="#15803D"
              />
              <path
                d="M10.5328 11.3858C10.3979 11.5032 10.2805 11.5794 10.1808 11.6146C10.0811 11.6498 9.98426 11.644 9.8904 11.597C9.8024 11.5501 9.70266 11.4621 9.5912 11.333L8.0116 9.49184L6.432 11.333C6.3264 11.4621 6.22666 11.5501 6.1328 11.597C6.03894 11.644 5.94214 11.6498 5.8424 11.6146C5.74267 11.5794 5.62533 11.5032 5.4904 11.3858C5.2968 11.2157 5.2 11.0632 5.2 10.9282C5.2 10.7933 5.28213 10.6349 5.4464 10.453L7.13977 8.47563L5.4464 6.50183C5.28213 6.32583 5.2 6.16743 5.2 6.02663C5.2 5.88583 5.2968 5.73036 5.4904 5.56023C5.61947 5.43703 5.73387 5.35783 5.8336 5.32263C5.93334 5.28743 6.03014 5.2933 6.124 5.34023C6.21786 5.38716 6.32054 5.4781 6.432 5.61303L8.0116 7.45757L9.5912 5.61303C9.70266 5.4781 9.80534 5.38716 9.8992 5.34023C9.99306 5.2933 10.0899 5.28743 10.1896 5.32263C10.2893 5.35783 10.4037 5.43703 10.5328 5.56023C10.7323 5.73036 10.8291 5.88583 10.8232 6.02663C10.8232 6.16743 10.7411 6.32583 10.5768 6.50183L8.88343 8.47563L10.5768 10.453C10.7411 10.6349 10.8232 10.7933 10.8232 10.9282C10.8291 11.0632 10.7323 11.2157 10.5328 11.3858Z"
                fill="white"
              />
            </svg>
          </div>
        )
      case 'text/plain':
        return (
          <div className="flex size-7 items-center justify-center rounded-[6px] bg-[#0000001A]">
            <svg
              width="16"
              height="17"
              viewBox="0 0 16 17"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M1.92 0.5C0.859609 0.5 0 1.35961 0 2.42L0 14.58C0 15.6404 0.859609 16.5 1.92 16.5H14.08C15.1404 16.5 16 15.6404 16 14.58V2.42C16 1.35961 15.1404 0.5 14.08 0.5L1.92 0.5ZM10.376 5.4864C10.2939 5.4688 10.2029 5.46 10.1032 5.46H5.9232C5.82934 5.46 5.7384 5.4688 5.6504 5.4864C5.5624 5.504 5.492 5.55387 5.4392 5.636C5.3864 5.71227 5.36 5.84427 5.36 6.032C5.36 6.21974 5.3864 6.35466 5.4392 6.4368C5.492 6.51307 5.5624 6.56293 5.6504 6.5864C5.7384 6.604 5.82934 6.6128 5.9232 6.6128H7.3576V10.96C7.3576 11.0715 7.36641 11.18 7.384 11.2856C7.4016 11.3853 7.45734 11.4675 7.55119 11.532C7.64506 11.5907 7.7976 11.62 8.00881 11.62C8.22586 11.62 8.3784 11.5907 8.46641 11.532C8.56027 11.4675 8.616 11.3853 8.63359 11.2856C8.65707 11.1859 8.66881 11.0803 8.66881 10.9688L8.66881 6.6128H10.0856C10.1853 6.6128 10.2792 6.604 10.3672 6.5864C10.4552 6.5688 10.5256 6.51894 10.5784 6.4368C10.6371 6.35466 10.6664 6.22267 10.6664 6.0408C10.6664 5.8472 10.6371 5.71227 10.5784 5.636C10.5256 5.55387 10.4581 5.504 10.376 5.4864Z"
                fill="black"
              />
              <path
                d="M10.1032 5.46C10.2029 5.46 10.2939 5.4688 10.376 5.4864C10.4581 5.504 10.5256 5.55387 10.5784 5.636C10.6371 5.71227 10.6664 5.8472 10.6664 6.0408C10.6664 6.22267 10.6371 6.35466 10.5784 6.4368C10.5256 6.51894 10.4552 6.5688 10.3672 6.5864C10.2792 6.604 10.1853 6.6128 10.0856 6.6128H8.66881L8.66881 10.9688C8.66881 11.0803 8.65707 11.1859 8.63359 11.2856C8.616 11.3853 8.56027 11.4675 8.46641 11.532C8.3784 11.5907 8.22586 11.62 8.00881 11.62C7.7976 11.62 7.64506 11.5907 7.55119 11.532C7.45734 11.4675 7.4016 11.3853 7.384 11.2856C7.36641 11.18 7.3576 11.0715 7.3576 10.96V6.6128H5.9232C5.82934 6.6128 5.7384 6.604 5.6504 6.5864C5.5624 6.56293 5.492 6.51307 5.4392 6.4368C5.3864 6.35466 5.36 6.21974 5.36 6.032C5.36 5.84427 5.3864 5.71227 5.4392 5.636C5.492 5.55387 5.5624 5.504 5.6504 5.4864C5.7384 5.4688 5.82934 5.46 5.9232 5.46H10.1032Z"
                fill="white"
              />
            </svg>
          </div>
        )

      case 'doc':
      case 'docx':
      case 'application/msword':
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        return (
          <div className="flex size-7 items-center justify-center rounded-[6px] bg-blue-50">
            <svg
              width="16"
              height="17"
              viewBox="0 0 16 17"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M1.92 0.5C0.859614 0.5 0 1.35961 0 2.42L0 14.58C0 15.6404 0.859614 16.5 1.92 16.5H14.08C15.1404 16.5 16 15.6404 16 14.58V2.42C16 1.35961 15.1404 0.5 14.08 0.5L1.92 0.5ZM5.986 11.5525C6.074 11.6112 6.19426 11.6405 6.3468 11.6405C6.51106 11.6405 6.6372 11.6083 6.7252 11.5437C6.81906 11.4733 6.88654 11.3971 6.9276 11.3149C6.96866 11.2328 6.99214 11.1771 6.998 11.1477L8.01 7.86533L9.022 11.1477C9.03374 11.1771 9.0572 11.2328 9.0924 11.3149C9.13346 11.3912 9.20094 11.4645 9.2948 11.5349C9.38866 11.6053 9.5148 11.6405 9.6732 11.6405C9.83746 11.6405 9.96066 11.6112 10.0428 11.5525C10.1308 11.4939 10.1924 11.4293 10.2276 11.3589C10.2687 11.2827 10.2951 11.2269 10.3068 11.1917L11.9612 6.39573C12.0082 6.23147 12.0286 6.09947 12.0228 5.99973C12.0228 5.89414 11.9876 5.80613 11.9172 5.73573C11.8468 5.66533 11.7236 5.59787 11.5476 5.53333C11.3833 5.47467 11.2455 5.4512 11.134 5.46293C11.0284 5.47467 10.9404 5.5216 10.87 5.60374C10.8055 5.68 10.7468 5.80027 10.694 5.96454L9.6028 9.23813L8.67 5.99093C8.6524 5.94987 8.626 5.8912 8.5908 5.81493C8.5556 5.73867 8.49106 5.66827 8.3972 5.60374C8.3092 5.5392 8.17426 5.50693 7.9924 5.50693C7.834 5.50693 7.7108 5.53626 7.6228 5.59493C7.5348 5.6536 7.47026 5.72107 7.4292 5.79733C7.394 5.8736 7.3676 5.94106 7.35 5.99973L6.426 9.22053L5.326 5.95573C5.2732 5.79146 5.2116 5.6712 5.1412 5.59493C5.07666 5.51867 4.9916 5.4776 4.886 5.47173C4.78626 5.46587 4.65426 5.48933 4.49 5.54213C4.23773 5.62427 4.0852 5.72693 4.0324 5.85013C3.9796 5.96747 3.99133 6.14934 4.0676 6.39573L5.722 11.1917C5.73374 11.2211 5.7572 11.2739 5.7924 11.3501C5.83346 11.4264 5.898 11.4939 5.986 11.5525Z"
                fill="#2563EB"
              />
              <path
                d="M6.3468 11.6405C6.19426 11.6405 6.074 11.6112 5.986 11.5525C5.898 11.4939 5.83346 11.4264 5.7924 11.3501C5.7572 11.2739 5.73374 11.2211 5.722 11.1917L4.0676 6.39573C3.99133 6.14934 3.9796 5.96747 4.0324 5.85013C4.0852 5.72693 4.23773 5.62427 4.49 5.54213C4.65426 5.48933 4.78626 5.46587 4.886 5.47173C4.9916 5.4776 5.07666 5.51867 5.1412 5.59493C5.2116 5.6712 5.2732 5.79146 5.326 5.95573L6.426 9.22053L7.35 5.99973C7.3676 5.94106 7.394 5.8736 7.4292 5.79733C7.47026 5.72107 7.5348 5.6536 7.6228 5.59493C7.7108 5.53626 7.834 5.50693 7.9924 5.50693C8.17426 5.50693 8.3092 5.5392 8.3972 5.60374C8.49106 5.66827 8.5556 5.73867 8.5908 5.81493C8.626 5.8912 8.6524 5.94987 8.67 5.99093L9.6028 9.23813L10.694 5.96454C10.7468 5.80027 10.8055 5.68 10.87 5.60374C10.9404 5.5216 11.0284 5.47467 11.134 5.46293C11.2455 5.4512 11.3833 5.47467 11.5476 5.53333C11.7236 5.59787 11.8468 5.66533 11.9172 5.73573C11.9876 5.80613 12.0228 5.89414 12.0228 5.99973C12.0286 6.09947 12.0082 6.23147 11.9612 6.39573L10.3068 11.1917C10.2951 11.2269 10.2687 11.2827 10.2276 11.3589C10.1924 11.4293 10.1308 11.4939 10.0428 11.5525C9.96066 11.6112 9.83746 11.6405 9.6732 11.6405C9.5148 11.6405 9.38866 11.6053 9.2948 11.5349C9.20094 11.4645 9.13346 11.3912 9.0924 11.3149C9.0572 11.2328 9.03374 11.1771 9.022 11.1477L8.01 7.86533L6.998 11.1477C6.99214 11.1771 6.96866 11.2328 6.9276 11.3149C6.88654 11.3971 6.81906 11.4733 6.7252 11.5437C6.6372 11.6083 6.51106 11.6405 6.3468 11.6405Z"
                fill="white"
              />
            </svg>
          </div>
        )

      case 'image/png':
      case 'image/jpeg':
      case 'image/jpg':
        return (
          <div className="flex size-[28px] items-center justify-center rounded-[6px] bg-teal-50">
            <svg
              width="16"
              height="17"
              viewBox="0 0 16 17"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M14.6791 11.7135L12.5924 6.83351C12.2124 5.94018 11.6458 5.43351 10.9991 5.40018C10.3591 5.36684 9.73911 5.81351 9.26578 6.66684L7.99911 8.94018C7.73245 9.42018 7.35245 9.70684 6.93912 9.74018C6.51911 9.78018 6.09912 9.56018 5.75912 9.12684L5.61245 8.94018C5.13912 8.34684 4.55245 8.06018 3.95245 8.12018C3.35245 8.18018 2.83912 8.59351 2.49912 9.26684L1.34578 11.5668C0.932449 12.4002 0.972449 13.3668 1.45912 14.1535C1.94578 14.9402 2.79245 15.4135 3.71912 15.4135L12.2258 15.4135C13.1191 15.4135 13.9524 14.9668 14.4458 14.2202C14.9524 13.4735 15.0324 12.5335 14.6791 11.7135Z"
                fill="#2DD4BF"
              />
              <path
                d="M4.64591 6.08674C5.89039 6.08674 6.89925 5.07789 6.89925 3.83341C6.89925 2.58893 5.89039 1.58008 4.64591 1.58008C3.40143 1.58008 2.39258 2.58893 2.39258 3.83341C2.39258 5.07789 3.40143 6.08674 4.64591 6.08674Z"
                fill="#2DD4BF"
              />
            </svg>
          </div>
        )
    }
  }

  return (
    <div
      className={cn(
        'flex w-full select-none items-center justify-end gap-[8px]',
        className
      )}
    >
      {renderThumbFile()}
      <div className="flex max-w-[calc(100%-36px)] flex-col">
        <Text type="subBody" variant="medium" ellipsis elementType="div">
          {file.name}
        </Text>
        <Text
          type="supportText"
          ellipsis
          className={clsx(
            '',
            file?.error ? 'text-Error-Color' : 'text-Placeholder'
          )}
        >
          {file?.error
            ? file.error
            : formatBytes({
                bytes: file.size,
                standard: 0,
              })}
        </Text>
      </div>
    </div>
  )
}

export default memo(FileItem)
