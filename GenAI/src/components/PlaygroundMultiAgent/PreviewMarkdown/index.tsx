/* eslint-disable @typescript-eslint/ban-ts-comment */
import Text from '@/components/Text'
import { addMultipleLineBreak } from '@/helpers'
import IconCsv from '@/pages/CiteMind/CiteMindChat/icons/IconCsv'
import IconDocx from '@/pages/CiteMind/CiteMindChat/icons/IconDocx'
import IconJpg from '@/pages/CiteMind/CiteMindChat/icons/IconJpg'
import IconPdf from '@/pages/CiteMind/CiteMindChat/icons/IconPdf'
import IconTxt from '@/pages/CiteMind/CiteMindChat/icons/IconTxt'
import { isNumber } from 'lodash'
import { memo } from 'react'
import { useTranslation } from 'react-i18next'
import ReactMarkdown from 'react-markdown'
import rehypeRaw from 'rehype-raw'
import remarkBreaks from 'remark-breaks'
import remarkGfm from 'remark-gfm'
import xss from 'xss'
import PopoverCitation from '../PopoverCitation'
import './styles.scss'

interface IPreviewMarkdown {
  message: string
  citation?: any
}

const PreviewMarkdown = ({ message, citation }: IPreviewMarkdown) => {
  const { t } = useTranslation()

  const citationAnswer = message.replace(/\[\d+\]/g, (match) => {
    return `<cite>${match.slice(1, -1)}</cite>`
  })

  const renderThumbFile = (type: string) => {
    switch (type) {
      case 'pdf':
        return <IconPdf />
      case 'xlsx':
      case 'csv':
        return <IconCsv />
      case 'txt':
        return <IconTxt />

      case 'docx':
        return <IconDocx />

      case 'png':
      case 'jpeg':
      case 'jpg':
        return <IconJpg />
    }
  }

  return (
    <ReactMarkdown
      className="reset-css"
      remarkPlugins={[[remarkGfm], [remarkBreaks]]}
      rehypePlugins={[rehypeRaw]}
      components={{
        cite: (props) => {
          // @ts-ignore
          const quoteIdx = parseInt(props?.node?.children[0]?.value)

          const messageCitationsMapToQuote = citation?.quotes?.find(
            (item: any) => parseInt(item?.number) === quoteIdx
          )

          const chunk = citation?.chunks?.find(
            (item: any) =>
              item?.chunk_id === messageCitationsMapToQuote?.chunk_id
          )

          const page = messageCitationsMapToQuote?.page

          return (
            <PopoverCitation
              overlay={
                <div className="flex min-h-[200px] w-[265px] overflow-hidden bg-white">
                  <div className="flex w-full flex-col">
                    <div className="grow border-b border-border-base-icon px-5 pb-3 pt-5">
                      <Text
                        type="subBody"
                        ellipsis
                        elementType="div"
                        multipleLine={12}
                        hasTooltip={false}
                      >
                        {messageCitationsMapToQuote?.quote}
                      </Text>
                    </div>
                    <div className="flex w-full items-center gap-2 px-3 py-2">
                      {renderThumbFile(chunk?.file_type)}
                      <div className="flex w-full flex-col">
                        <Text
                          elementType="div"
                          ellipsis
                          type="subBody"
                          variant="medium"
                          className="max-w-[calc(100%-40px)]"
                        >
                          {chunk?.file_name}
                        </Text>

                        {isNumber(page) && (
                          <Text
                            elementType="div"
                            ellipsis
                            type="supportText"
                            className="text-Secondary-Color"
                          >
                            {t('common.page')} {page + 1}
                          </Text>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              }
              dataCitation={{
                page,
                quote: messageCitationsMapToQuote?.quote,
                chunk: chunk?.content,
                file_url: chunk?.file_url,
                file_name: chunk?.file_name,
                file_type: chunk?.file_type,
              }}
            >
              {quoteIdx}
            </PopoverCitation>
          )
        },
      }}
    >
      {xss(addMultipleLineBreak(citationAnswer))}
    </ReactMarkdown>
  )
}

export default memo(PreviewMarkdown)
