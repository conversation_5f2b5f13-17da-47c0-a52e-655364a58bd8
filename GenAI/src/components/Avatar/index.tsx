import Text from '@/components/Text'
import { cn, getUrlImage, isFullPath } from '@/helpers'
import clsx from 'clsx'
import { isFunction, isNil } from 'lodash'
import { ReactNode, memo, useEffect, useMemo, useState } from 'react'

interface IAvatarProps {
  avatarUrl?: string
  variant?: 'circle' | 'square'
  size?:
    | 'extra-small'
    | 'smaller'
    | 'small'
    | 'medium'
    | 'semi-large'
    | 'large'
    | 'larger'
  name?: string
  hasBorder?: boolean
  avatarDefault?: ReactNode
  className?: string
  imgClassName?: string
  [key: string]: any
}

const Avatar = (props: IAvatarProps) => {
  const {
    avatarUrl,
    variant = 'circle',
    size = 'medium',
    name = '',
    hasBorder = false,
    avatarDefault,
    className,
    imgClassName = '',
    ...rest
  } = props

  const [isError, setError] = useState(false)

  useEffect(() => {
    if (!avatarUrl) setError(true)
    else setError(false)
  }, [avatarUrl])

  const abbreviateName = useMemo(
    () => name?.slice(0, 1)?.toUpperCase?.(),
    [name]
  )

  return (
    <div
      className={cn(
        className,
        'avatar flex cursor-pointer items-center justify-center overflow-hidden',
        variant === 'circle' && '!rounded-full',
        {
          'size-5 min-w-5 rounded-md': size === 'extra-small',
          'size-6 min-w-6 rounded-md': size === 'smaller',
          'size-7 min-w-7 rounded-lg': size === 'small',
          'size-8 min-w-8 rounded-lg': size === 'medium',
          'size-[36px] min-w-[36px] rounded-lg': size === 'semi-large',
          'size-[42px] min-w-[42px] rounded-lg': size === 'large',
          'size-16 min-w-16 rounded-lg': size === 'larger',
        },
        {
          'border-[0.5px] border-solid border-border-base-icon':
            hasBorder && avatarUrl && !isError,
        },
        {
          'bg-Main-05': isError && isNil(avatarDefault),
        }
      )}
      {...rest}
    >
      {avatarUrl && !isError ? (
        <img
          className={clsx(
            imgClassName,
            'h-full w-full object-cover',
            variant === 'circle' && '!rounded-full',
            {
              'rounded-md': size === 'extra-small' || size === 'smaller',
              'rounded-lg':
                size === 'small' ||
                size === 'medium' ||
                size === 'semi-large' ||
                size === 'large' ||
                size === 'larger',
            }
          )}
          src={isFullPath(avatarUrl) ? avatarUrl : getUrlImage(avatarUrl)}
          alt={name}
          onError={() => setError(true)}
          onLoad={() => setError(false)}
        />
      ) : isFunction(avatarDefault) ? (
        avatarDefault()
      ) : (
        (avatarDefault ?? (
          <Text
            className={clsx('bg-Main-Color bg-clip-text text-transparent', {
              '!text-subBody': size === 'extra-small',
              '!text-body': size === 'smaller',
              '!text-subheading': size === 'small',
              '!text-heading': size === 'medium',
              '!text-[24px] !leading-[36px]': size === 'semi-large',
              '!text-title': size === 'large',
              '!text-[36px] leading-[54px]': size === 'larger',
            })}
            variant="semibold"
          >
            {abbreviateName}
          </Text>
        ))
      )}
    </div>
  )
}

export default memo(Avatar)
