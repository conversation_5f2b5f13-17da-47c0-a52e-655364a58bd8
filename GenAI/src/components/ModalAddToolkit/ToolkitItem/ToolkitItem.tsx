import { ToolItem_Input } from '@/apis/client'
import Icon from '@/assets/icon/Icon'
import Avatar from '@/components/Avatar'
import Button from '@/components/Button'
import IconButton from '@/components/IconButton'
import Text from '@/components/Text'
import Tooltip from '@/components/Tooltip'
import { TOOL_FUNCTIONALITY, TOOL_TYPE } from '@/pages/Tools/const'
import { colors } from '@/theme'
import clsx from 'clsx'
import { memo, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import IconBuiltInTool from './IconBuiltInTool'
import './index.scss'

export enum ToolkitItemMode {
  DEFAULT = 'default',
  SELECTED = 'selected',
  PREVIEW = 'preview',
}

interface IProps {
  toolkit: ToolItem_Input
  isActive?: boolean
  isConfigured?: boolean
  onClick?: NoneToVoidFunction
  disableSelect?: boolean
  openModalToolParameters: NoneToVoidFunction
  isError?: boolean
  className?: string
  mode?:
    | ToolkitItemMode.DEFAULT
    | ToolkitItemMode.SELECTED
    | ToolkitItemMode.PREVIEW
  onAdd?: () => void
  onRemove?: () => void
  onDelete?: () => void
}

const ToolkitItem = ({
  toolkit,
  isActive = false,
  isConfigured = false,
  onClick,
  disableSelect = false,
  openModalToolParameters,

  isError = false,
  className,
  mode = ToolkitItemMode.DEFAULT,
  onAdd,
  onRemove,
  onDelete,
}: IProps) => {
  const { t } = useTranslation()

  const { logo, name, description, tool_type, tool_functionality } = toolkit

  const isBuiltInTool = useMemo(
    () => tool_type === TOOL_TYPE.BUILT_IN,
    [tool_type]
  )

  const isGeneralTool = useMemo(
    () => tool_functionality === TOOL_FUNCTIONALITY.GENERAL,
    [tool_functionality]
  )

  const isShowToolConfiguration = useMemo(
    () => isBuiltInTool && isGeneralTool && isActive && isConfigured,
    [isBuiltInTool, isGeneralTool, isActive, isConfigured]
  )

  const widthOfText = useMemo(() => {
    if ((isBuiltInTool && !isActive) || (isActive && !isBuiltInTool)) {
      return 'w-[430px]'
    }
    if (isBuiltInTool && isActive) {
      return 'w-[375px]'
    }
    return 'w-full'
  }, [isBuiltInTool, isActive])

  const _onClick = useMemo(() => {
    if (disableSelect && !isActive) {
      return () => {}
    }
    return onClick
  }, [disableSelect, isActive, onClick])

  const _onAdd = useMemo(() => {
    if (disableSelect || isActive) {
      return () => {}
    }
    return onAdd
  }, [disableSelect, isActive, onAdd])

  return (
    <div
      className={clsx(
        'flex w-full select-none items-center justify-between gap-4 rounded-lg py-1.5 pl-3 pr-2 hover:bg-Hover-Color'
      )}
      onClick={_onClick}
    >
      <div
        className={clsx(
          'flex flex-col gap-1',
          mode === ToolkitItemMode.SELECTED
            ? 'max-w-[calc(100%-100px)]'
            : 'max-w-[calc(100%-80px)]',
          className
        )}
      >
        <div className="flex w-full items-center gap-2">
          <div className="relative">
            <Avatar
              name={name}
              avatarUrl={logo!}
              size="medium"
              variant="square"
              hasBorder
              avatarDefault={
                <div className="flex size-8 items-center justify-center rounded-lg border border-border-base-icon bg-Background-Color">
                  <Icon
                    name="tool-01"
                    size={18}
                    gradient={['#642B734D', '#C6426E4D']}
                  />
                </div>
              }
            />
            {isBuiltInTool && (
              <div className="absolute -left-1 -top-1">
                <Tooltip text={t('add_tool.required_params')}>
                  <div>
                    <IconBuiltInTool />
                  </div>
                </Tooltip>
              </div>
            )}
          </div>
          <div className={clsx('flex w-[calc(100%-40px)] flex-col')}>
            <Text
              type="subBody"
              variant="medium"
              className={clsx('text-Primary-Color')}
              elementType="div"
              ellipsis
            >
              {name}
            </Text>
            {description && (
              <Text
                type="supportText"
                className={clsx(widthOfText, 'text-Secondary-Color')}
                elementType="div"
                ellipsis
                multipleLine={2}
              >
                {description}
              </Text>
            )}
          </div>
        </div>
        {/* {tool_category?.name && (
          <Text
            type="subBody"
            variant="medium"
            elementType="div"
            className="bg-Main-Color bg-clip-text px-1 text-transparent"
          >
            {tool_category.name}
          </Text>
        )} */}
      </div>

      {mode === ToolkitItemMode.DEFAULT && (
        <div className="flex items-center gap-4">
          {isShowToolConfiguration && (
            <IconButton
              nameIcon="Bold-SettingsFineTuning-Tuning2"
              colorIcon={
                isError ? colors['Error-Color'] : colors['Primary-Color']
              }
              hoverColor={
                isError ? colors['Error-Color'] : ['#642B73', '#C6426E']
              }
              tooltipText="Configure tool params"
              onClick={openModalToolParameters}
            />
          )}

          <Button
            className={clsx('!min-w-16 rounded-md px-2')}
            text={isActive ? t('common.added') : t('common.add')}
            type={isActive ? 'default' : 'secondary'}
            disabled={isActive || disableSelect}
            size="small"
            onClick={() => _onAdd?.()}
          />
        </div>
      )}

      {mode === ToolkitItemMode.SELECTED && (
        <Button
          className="!min-w-[84px] rounded-md px-2"
          text={t('common.remove')}
          type="secondary"
          size="small"
          onClick={() => onRemove?.()}
        />
      )}

      {mode === ToolkitItemMode.PREVIEW && (
        <div className="flex items-center px-2">
          <div
            className={clsx(
              'gradient-settings-02-stroke flex cursor-pointer rounded-lg p-1',
              isError && 'gradient-settings-02-stroke-error'
            )}
            onClick={openModalToolParameters}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 16 17"
              fill="none"
            >
              <path
                // eslint-disable-next-line max-len
                d="M6.26344 13.4147L6.65307 14.291C6.7689 14.5519 6.95792 14.7735 7.19722 14.9291C7.43652 15.0846 7.71581 15.1674 8.00122 15.1673C8.28663 15.1674 8.56592 15.0846 8.80522 14.9291C9.04452 14.7735 9.23354 14.5519 9.34937 14.291L9.739 13.4147C9.8777 13.1038 10.111 12.8446 10.4057 12.674C10.7022 12.5029 11.0452 12.4301 11.3857 12.4658L12.339 12.5673C12.6228 12.5973 12.9092 12.5444 13.1635 12.4149C13.4177 12.2854 13.629 12.0849 13.7716 11.8377C13.9144 11.5907 13.9824 11.3075 13.9674 11.0226C13.9524 10.7376 13.8551 10.4632 13.6871 10.2325L13.1227 9.45695C12.9217 9.17874 12.8143 8.84386 12.816 8.50065C12.816 8.15838 12.9244 7.82489 13.1257 7.54806L13.6901 6.7725C13.858 6.54182 13.9554 6.26736 13.9704 5.98243C13.9854 5.6975 13.9173 5.41435 13.7746 5.16732C13.632 4.92014 13.4207 4.71964 13.1664 4.59014C12.9121 4.46063 12.6257 4.40767 12.342 4.43769L11.3886 4.53917C11.0482 4.57492 10.7052 4.50207 10.4086 4.33102C10.1134 4.15949 9.88001 3.89889 9.74196 3.58658L9.34937 2.71028C9.23354 2.44943 9.04452 2.22779 8.80522 2.07225C8.56592 1.91671 8.28663 1.83394 8.00122 1.83398C7.71581 1.83394 7.43652 1.91671 7.19722 2.07225C6.95792 2.22779 6.7689 2.44943 6.65307 2.71028L6.26344 3.58658C6.1254 3.89889 5.89202 4.15949 5.59678 4.33102C5.30025 4.50207 4.95722 4.57492 4.61678 4.53917L3.66048 4.43769C3.3767 4.40767 3.09031 4.46063 2.83602 4.59014C2.58174 4.71964 2.37049 4.92014 2.22789 5.16732C2.0851 5.41435 2.01708 5.6975 2.03207 5.98243C2.04705 6.26736 2.14441 6.54182 2.31233 6.7725L2.87678 7.54806C3.07807 7.82489 3.18647 8.15838 3.18641 8.50065C3.18647 8.84293 3.07807 9.17642 2.87678 9.45324L2.31233 10.2288C2.14441 10.4595 2.04705 10.7339 2.03207 11.0189C2.01708 11.3038 2.0851 11.587 2.22789 11.834C2.37063 12.081 2.58191 12.2814 2.83615 12.4109C3.0904 12.5404 3.37672 12.5934 3.66048 12.5636L4.61381 12.4621C4.95426 12.4264 5.29729 12.4992 5.59381 12.6703C5.89017 12.8413 6.12462 13.102 6.26344 13.4147Z"
                stroke="#766D72"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M8.00003 10.5007C9.1046 10.5007 10 9.60522 10 8.50065C10 7.39608 9.1046 6.50065 8.00003 6.50065C6.89546 6.50065 6.00003 7.39608 6.00003 8.50065C6.00003 9.60522 6.89546 10.5007 8.00003 10.5007Z"
                stroke="#766D72"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <defs>
                <linearGradient
                  id="gradient-settings-02-#642B73-#C6426E"
                  x1="0%"
                  y1="0%"
                  x2="100%"
                  y2="100%"
                >
                  <stop stopColor="#642B73" stopOpacity="1"></stop>
                  <stop offset="1" stopColor="#C6426E" stopOpacity="1"></stop>
                </linearGradient>
              </defs>
            </svg>
          </div>
          <IconButton
            className="rounded-lg p-1"
            nameIcon="Trash-04"
            sizeIcon={16}
            colorIcon="#766D72"
            hoverColor={['#642B73', '#C6426E']}
            onClick={onDelete}
          />
        </div>
      )}
    </div>
  )
}

export default memo(ToolkitItem)
