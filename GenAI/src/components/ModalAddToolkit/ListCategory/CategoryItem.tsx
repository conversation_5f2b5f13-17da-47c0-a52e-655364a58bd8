import { ToolCategoryPublic } from '@/apis/client'
import Text from '@/components/Text'
import clsx from 'clsx'
import { useTranslation } from 'react-i18next'
import { twMerge } from 'tailwind-merge'

interface Props {
  isMain?: boolean
  isActive?: boolean
  onClick?: (category: any) => void
  category?: ToolCategoryPublic
  number?: number | null
}

const CategoryItem = ({
  isMain,
  isActive,
  category,
  number,
  onClick,
}: Props) => {
  const { t } = useTranslation()

  return (
    <div
      className={twMerge(
        'flex h-[26px] items-center justify-center rounded-full bg-Main-Color p-[1px]',
        clsx({ 'bg-transparent': !isActive })
      )}
    >
      <div className="flex items-center justify-center rounded-full bg-white">
        <div
          onClick={() => {
            onClick && onClick(category)
          }}
          className={twMerge(
            clsx(
              'bg-Base-01',
              'flex w-fit cursor-pointer select-none items-center justify-center gap-[4px] rounded-full px-2 py-1 duration-300',
              {
                'bg-Main-03': isActive,
                'h-[24px]': isActive,
                'h-[26px]': !isActive,
                'hover:bg-Hover-2': !isActive,
                'w-[80px]': isMain,
              }
            )
          )}
        >
          {category?.name?.trim() !== '' && (
            <Text
              type="subBody"
              className={clsx('whitespace-nowrap', '!text-Primary-Color')}
            >
              {category?.name?.trim() ?? t('add_tool.my_tools')}
            </Text>
          )}

          {!isMain && (
            <span
              className={twMerge(
                'text-[10px] leading-[15px] text-Secondary-Color'
              )}
            >
              {number ?? 0}
            </span>
          )}
        </div>
      </div>
    </div>
  )
}

export default CategoryItem
