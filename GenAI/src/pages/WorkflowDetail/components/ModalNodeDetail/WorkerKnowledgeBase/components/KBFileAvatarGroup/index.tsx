import { KnowledgeBaseItem } from '@/apis/client'
import Icon from '@/assets/icon/Icon'
import Avatar from '@/components/Avatar'
import { CustomExternalKBPublic } from '@/components/ModalAddKnowledgeBaseExternal'
import Text from '@/components/Text'
import { getIconNameByFileIcon } from '@/components/Upload/helper'
import clsx from 'clsx'
import { useMemo } from 'react'

interface KBFileAvatarGroupProps {
  className?: string
  knowledgeBases: KnowledgeBaseItem[] | CustomExternalKBPublic[]
  max?: number
}

const KBFileAvatarGroup = ({
  className,
  knowledgeBases = [],
  max = 20,
}: KBFileAvatarGroupProps) => {
  const showedKnowledgeBases = useMemo(
    () => knowledgeBases.slice(0, max),
    [knowledgeBases, max]
  )
  const hiddenKnowledgeBases = useMemo(
    () => knowledgeBases.slice(max),
    [knowledgeBases, max]
  )

  const contentMoreAvatar = useMemo(() => {
    const hiddenKnowledgeBasesLength = hiddenKnowledgeBases.length
    if (!hiddenKnowledgeBasesLength) {
      return ''
    }
    if (hiddenKnowledgeBasesLength > 99) {
      return '99+'
    }

    return `+${hiddenKnowledgeBasesLength}`
  }, [hiddenKnowledgeBases])

  return (
    <div className={clsx('flex items-center', className)}>
      {showedKnowledgeBases.map((knowledgeBase) => (
        <Avatar
          key={knowledgeBase.id}
          name="logo"
          avatarUrl=""
          size="medium"
          variant="circle"
          className="z-10 mr-[-10px] flex min-h-8 min-w-8 !border bg-Base-01"
          hasBorder
          avatarDefault={
            knowledgeBase.file_extension ? (
              <Icon
                size={18}
                name={getIconNameByFileIcon(knowledgeBase.file_extension!)}
              />
            ) : (
              <div className="flex size-8 items-center justify-center rounded-lg border border-border-base-icon bg-Background-Color">
                <Icon
                  name="Custom-storage"
                  size={22}
                  gradient={['#642B734D', '#C6426E4D']}
                />
              </div>
            )
          }
        />
      ))}
      {contentMoreAvatar && (
        <Avatar
          size="medium"
          variant="circle"
          imgClassName="!h-6 !w-6"
          className="z-10 mr-[-10px] overflow-hidden !border bg-Base-01"
          hasBorder
          avatarDefault={
            <Text
              value={contentMoreAvatar}
              variant="medium"
              type="supportText"
              className="text-Tertiary-Color"
            />
          }
        />
      )}
    </div>
  )
}

export default KBFileAvatarGroup
