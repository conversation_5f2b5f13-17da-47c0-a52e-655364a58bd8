import {
  ComposioExpectedParam,
  CredentialCheckStatusEnum,
  CredentialType,
  ToolEnvironmentVariable,
  ToolInputParameter,
  credentialsCheckCredentialStatusApi,
  credentialsCreateApiKeyCredentialsApi,
  credentialsCreateCredentialsApi,
  credentialsGetCredentialsApi,
  credentialsGetExpectedParamsApi,
  toolsGetToolParametersApi,
} from '@/apis/client'
import BaseModal from '@/components/BaseModal'
import Button from '@/components/Button'
import EmptyData from '@/components/EmptyData'
import IconButton from '@/components/IconButton'
import Input from '@/components/Input'
import InputWithSecret from '@/components/InputWithSecret'
import Message from '@/components/Message'
import { GeneralToolParamConfigurationData } from '@/components/ModalAddToolkit/types'
import Spin from '@/components/Spin'
import Text from '@/components/Text'
import TextArea from '@/components/TextArea'
import { HTTP_STATUS_CODE } from '@/constants'
import { useGroupTool } from '@/hooks/useGroupTool'
import { useMyProfile } from '@/hooks/useMyProfile'
import { useSubcriptionPlan } from '@/hooks/useSubcriptionPlan'
import {
  AUTH_TYPE_LABEL,
  LINKEDIN_AUTH_TYPE_LABEL,
  MICROSOFT_OUTLOOK_OAUTH2_API,
} from '@/pages/Tools/const'
import { colors } from '@/theme'
import { isNil } from 'lodash'
import { memo, useCallback, useEffect, useRef, useState } from 'react'
import IconOutlook from './IconOutlook'
import InputByType, { EParamType } from './InputByType'

import { useTranslation } from 'react-i18next'
import IconLinkedin from './IconLinkedin'
import IconWarning from './IconWarning'
import SelectConnection from './component/SelectConnection'

interface IToolEnvironmentVariable extends ToolEnvironmentVariable {
  feIsSecret: boolean
}

interface ISelectOption {
  id: string
  value: string
  name: string
}

const ModalGeneralToolParameters = ({
  onClose,
  onSave,
  toolId,
  generalToolParamConfigurations,
  authType,
  logo,
  groupId,
}: IModalGeneralToolParameters) => {
  const { t } = useTranslation()
  const [params, setParams] = useState<ToolInputParameter[]>([])
  const [envs, setEnvs] = useState<IToolEnvironmentVariable[]>([])
  const [loading, setLoading] = useState(true)
  const [initializing, setInitializing] = useState(true)
  const [credentials, setCredentials] = useState<Credential[]>([])
  const [selectedCredentialId, setSelectedCredentialId] = useState<
    string | undefined
  >(undefined)
  const [whenToUse, setWhenToUse] = useState<string>('')
  const [isErrorPrompt, setIsErrorPrompt] = useState<boolean>(false)
  const [expectedParams, setExpectedParams] = useState<ComposioExpectedParam[]>(
    []
  )
  const [addNewConnectionMode, setAddNewConnectionMode] = useState(false)

  const [isDisabled, setIsDisabled] = useState(false)
  const [isError, setIsError] = useState(false)
  const [showErrorFullConnection, setShowErrorFullConnection] = useState(false)
  const [credentialValueDefault, setCredentialValueDefault] = useState('')
  const { groupTool } = useGroupTool()

  const { myProfile } = useMyProfile()
  const { checkSubscriptionPlan } = useSubcriptionPlan()
  const [isLoadingCreate, setIsLoadingCreate] = useState(false)

  const fetchCredentials = useCallback(async () => {
    const res = await credentialsGetCredentialsApi({
      query: {
        auth_type: authType as CredentialType,
        page_size: 200,
        tool_group_id: groupId,
        status: 'SUCCESS',
      },
    })

    if (res.status === HTTP_STATUS_CODE.SUCCESS) {
      const credentials = res.data?.data?.data || []
      setCredentials(credentials)
    }
  }, [authType, groupId])

  const fetchExpectParams = useCallback(async () => {
    const res = await credentialsGetExpectedParamsApi({
      path: {
        tool_id: toolId,
      },
    })

    if (res.status === HTTP_STATUS_CODE.SUCCESS) {
      setExpectedParams(res.data || [])
    }
  }, [toolId])

  const openAuthPopup = async (
    authUrl: string,
    options: {
      onSuccess?: () => void
      onClose?: () => void
      credentialId?: string
    }
  ) => {
    const { onSuccess, onClose, credentialId } = options

    const width = 705
    const height = 530
    const left = (window.screen.width - width) / 2
    const top = (window.screen.height - height) / 2

    const popup = window.open(
      authUrl,
      getNameParam(),
      `menubar=no,location=no,resizable=no,scrollbars=no,status=no,width=${width},height=${height},top=${top},left=${left}`
    )

    // Listen for messages from popup
    const handleMessage = (event: MessageEvent) => {
      if (event.origin !== window.location.origin) return

      if (event.data.type === 'SSO_SUCCESS') {
        if (event.data.success) {
          fetchCredentials()
          // Update ___credential_id_ when connection is successful
          const newParams = [...params]
          const credentialIdParam = newParams.find(
            (p) => p.name === '___credential_id_'
          )

          if (credentialIdParam && credentialId) {
            credentialIdParam.default_value = credentialId
            setParams(newParams)
            setSelectedCredentialId(credentialId)
          }

          onSuccess?.()
        } else {
          Message.error({
            message: t('connection.reconnect_failed'),
          })
        }

        popup?.close()
        onClose?.()
      }
    }

    window.addEventListener('message', handleMessage)

    // Check popup status every 500ms
    const checkPopup = setInterval(() => {
      if (popup?.closed) {
        clearInterval(checkPopup)
        window.removeEventListener('message', handleMessage)
        onClose?.()
      }
    }, 500)

    // Return cleanup function
    return () => {
      clearInterval(checkPopup)
      window.removeEventListener('message', handleMessage)
      popup?.close()
    }
  }

  const getNameParam = () => {
    switch (authType) {
      case CredentialType.MICROSOFT_OUTLOOK_OAUTH2API:
        return AUTH_TYPE_LABEL
      case CredentialType.LINKED_IN_OAUTH2API:
        return LINKEDIN_AUTH_TYPE_LABEL
      default:
        return `${groupTool?.find((group) => group.id === groupId)?.name} Connection`
    }
  }

  const handleOpenAuthPopup = useCallback(async () => {
    const checkFullConnection = await checkSubscriptionPlan()
    if (checkFullConnection) {
      setShowErrorFullConnection(true)
      return
    } else {
      const res = await credentialsCreateCredentialsApi({
        body: {
          tool_id: toolId,
          type: authType as CredentialType,
          name: getNameParam(),
        },
      })

      if (res.data?.data?.authorization_url) {
        const cleanup = await openAuthPopup(res.data?.data?.authorization_url, {
          credentialId: res.data?.data?.id,
        })
        // Store cleanup function in ref to be called on unmount
        cleanupRef.current = cleanup
      } else {
        Message.error({
          message: t('workflowdetail.failed_to_get_authorization_url'),
        })
      }
    }
  }, [toolId, fetchCredentials, params, getNameParam])

  const createCredentialsKey = async () => {
    try {
      setIsLoadingCreate(true)
      const checkFullConnection = await checkSubscriptionPlan()

      if (checkFullConnection) {
        setShowErrorFullConnection(true)
        return
      } else {
        const res = await credentialsCreateApiKeyCredentialsApi({
          body: {
            tool_id: toolId,
            type: authType as CredentialType,
            name: `Connection to ${myProfile?.first_name}`,
            expected_params: expectedParams.map((param) => ({
              name: param.name,
              value: param.value,
            })),
          },
        })

        if (res.status === HTTP_STATUS_CODE.SUCCESS) {
          const newParams = [...params]
          const credentialIdParam = newParams.find(
            (p) => p.name === '___credential_id_'
          )

          if (credentialIdParam && res.data?.data.id) {
            credentialIdParam.default_value = res.data?.data.id
            setParams(newParams)
            setSelectedCredentialId(res.data?.data.id)
          }

          setExpectedParams((prev) =>
            prev.map((param) => ({ ...param, value: '' }))
          )
          fetchCredentials()
          setAddNewConnectionMode(false)
        }
      }
    } catch (error) {
      Message.error({
        message: t('tool_category.something_went_wrong'),
      })
    } finally {
      setIsLoadingCreate(false)
    }
  }

  // Add ref to store cleanup function
  const cleanupRef = useRef<(() => void) | null>(null)

  // Add useEffect for cleanup on unmount
  useEffect(() => {
    return () => {
      if (cleanupRef.current) {
        cleanupRef.current()
        cleanupRef.current = null
      }
    }
  }, [])

  const [isReconnecting, setIsReconnecting] = useState(false)
  const [isExpired, setIsExpired] = useState(false)

  const handleReconnect = async () => {
    if (
      isReconnecting ||
      !selectedCredentialId ||
      !authType ||
      !authType.toLowerCase().includes('oauth2')
    )
      return
    setIsReconnecting(true)

    try {
      const res = await credentialsCreateCredentialsApi({
        body: {
          type: authType as CredentialType,
          name:
            authType === MICROSOFT_OUTLOOK_OAUTH2_API
              ? AUTH_TYPE_LABEL
              : LINKEDIN_AUTH_TYPE_LABEL,
          credential_id: selectedCredentialId,
        },
      })

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        const auth_url = res.data?.data?.authorization_url || ''
        await openAuthPopup(auth_url, {
          credentialId: selectedCredentialId,
          onClose: () => setIsReconnecting(false),
          onSuccess: () => {
            setIsExpired(false)
          },
        })
      } else {
        Message.error({
          message: t('connection.reconnect_failed'),
        })
      }
    } catch (error) {
      Message.error({
        message: t('common.something_went_wrong'),
      })
    }
  }

  const fetchInfo = useCallback(async () => {
    try {
      setLoading(true)
      const res = await toolsGetToolParametersApi({
        path: {
          tool_id: toolId,
        },
      })
      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        const params = res.data?.data?.input_parameters || []
        const mappingParams = params.map((param) => {
          const defaultValue: any =
            generalToolParamConfigurations?.inputParameters
              ? generalToolParamConfigurations?.inputParameters?.find(
                  (genParam) => genParam.name === param.name
                )?.value
              : res.data?.data?.default_parameters?.find(
                  (genParam) => genParam.name === param.name
                )?.value

          if (defaultValue || defaultValue === false) {
            param.default_value = defaultValue
          } else {
            param.default_value = ''
          }

          return param
        })

        // Add hidden ___credential_id_ parameter if it doesn't exist and not GENERAL tool
        if (
          authType &&
          !mappingParams.find((p) => p.name === '___credential_id_')
        ) {
          const defaultValue =
            generalToolParamConfigurations?.inputParameters?.find(
              (genParam) => genParam.name === '___credential_id_'
            )?.value
          mappingParams.push({
            name: '___credential_id_',
            type: 'str',
            default_value: typeof defaultValue === 'string' ? defaultValue : '',
            description: 'Hidden parameter to store credential ID',
          })
        }

        // Handle ___prompt_ for non-GENERAL tools
        if (
          authType &&
          !authType.includes('Composio') &&
          !mappingParams.find((p) => p.name === '___prompt_')
        ) {
          const defaultValue =
            generalToolParamConfigurations?.inputParameters?.find(
              (genParam) => genParam.name === '___prompt_'
            )?.value
          mappingParams.push({
            name: '___prompt_',
            type: 'str',
            default_value: typeof defaultValue === 'string' ? defaultValue : '',
            description: 'Hidden parameter to store when to use description',
          })
          setWhenToUse(defaultValue as string)
        }

        setParams(mappingParams)
        const credentialId = mappingParams.find(
          (p) => p.name === '___credential_id_'
        )?.default_value
        if (typeof credentialId === 'string') {
          setSelectedCredentialId(credentialId)
        }

        const envs = res.data?.data?.environment_variables || []
        const mapping = envs.map((env) => {
          const defaultValue =
            generalToolParamConfigurations?.environmentVariables
              ? generalToolParamConfigurations?.environmentVariables?.find(
                  (genEnv) => genEnv.name === env.name
                )?.value
              : res.data?.data?.default_envs?.find(
                  (genEnv) => genEnv.name === env.name
                )?.value

          return {
            ...env,
            feIsSecret: !env.is_secret,
            default_value: defaultValue,
          }
        })
        setEnvs(mapping)
      } else {
        setParams([])
        setEnvs([])
      }
    } catch (error) {
      console.log(error)
      setParams([])
      setEnvs([])
    } finally {
      setLoading(false)
    }
  }, [toolId, generalToolParamConfigurations, authType])

  useEffect(() => {
    setInitializing(true)
    setTimeout(() => {
      setInitializing(false)
    }, 500)
    fetchInfo()
    if (authType) {
      fetchCredentials()
      if (
        authType === CredentialType.COMPOSIO_APIKEY ||
        authType === CredentialType.COMPOSIO_BEARER_TOKEN
      ) {
        fetchExpectParams()
      }
    }
  }, [authType])

  const handleChangeSelectedCredential = (option: ISelectOption) => {
    setSelectedCredentialId(option.id)
    if (isExpired) {
      setIsExpired(false)
    }
    if (isDisabled) {
      setIsDisabled(false)
    }
    if (isError) {
      setIsError(false)
    }
    if (showErrorFullConnection) {
      setShowErrorFullConnection(false)
    }

    const newParams = [...params]
    const credentialIdParam = newParams.find(
      (p) => p.name === '___credential_id_'
    )
    if (credentialIdParam) {
      credentialIdParam.default_value = option.id
      setParams(newParams)
    }

    if (option.id === selectedCredentialId) {
      checkStatusConnection(option.id)
    }
  }

  const checkStatusConnection = async (selectedCredentialId: string) => {
    const res = await credentialsCheckCredentialStatusApi({
      path: {
        credential_id: selectedCredentialId,
      },
    })

    if (res.status === HTTP_STATUS_CODE.SUCCESS) {
      const { data } = res.data as any
      const isError = data?.status === CredentialCheckStatusEnum.ERROR
      const isDisabled = data?.status === CredentialCheckStatusEnum.DISABLED
      const isExpired = data?.status === CredentialCheckStatusEnum.EXPIRED

      if (isExpired) {
        setIsExpired(true)

        setCredentialValueDefault(data.detail?.name)
      } else {
        setIsExpired(false)
      }

      if (isDisabled) {
        setIsDisabled(true)

        setCredentialValueDefault(data.detail?.name)
      } else {
        setIsDisabled(false)
      }

      if (isError) {
        setIsError(true)

        setCredentialValueDefault(data.detail?.name)
      } else {
        setIsError(false)
      }
    } else {
      Message.error({
        message: t('tool_category.something_went_wrong'),
      })
    }
  }

  useEffect(() => {
    if (!selectedCredentialId) return
    checkStatusConnection(selectedCredentialId)
  }, [selectedCredentialId])

  const handleRemoveCredentials = async () => {
    if (!selectedCredentialId) {
      return
    }

    setIsDisabled(false)
    setCredentialValueDefault('')
    setIsError(false)
    setIsExpired(false)

    setSelectedCredentialId(undefined)
    setParams((prev) =>
      prev.map((p) =>
        p.name === '___credential_id_' ? { ...p, default_value: '' } : p
      )
    )
  }

  const getLogo = () => {
    switch (authType) {
      case CredentialType.MICROSOFT_OUTLOOK_OAUTH2API:
        return <IconOutlook />
      case CredentialType.LINKED_IN_OAUTH2API:
        return <IconLinkedin />
      default:
        return <img src={logo!} alt="logo" width={18} />
    }
  }

  const getText = () => {
    switch (authType) {
      case CredentialType.MICROSOFT_OUTLOOK_OAUTH2API:
        return t('workflowdetail.sign_in_with_microsoft_outlook')
      case CredentialType.LINKED_IN_OAUTH2API:
        return t('workflowdetail.sign_in_with_linkedin')
      default:
        return `Sign in with ${groupTool?.find((group) => group.id === groupId)?.name}`
    }
  }

  const getError = () => {
    if (showErrorFullConnection) {
      return (
        <div className="flex flex-col items-center">
          <div className="flex items-center justify-center gap-1">
            <IconWarning />

            <Text type="subBody" variant="regular" className="text-Error-Color">
              {t('workflowdetail.max_connections_exceeded')}
            </Text>
          </div>
          <Text type="subBody" variant="regular" className="text-Error-Color">
            {t('workflowdetail.please_upgrade_your_plans_to_continue')}
          </Text>
        </div>
      )
    } else if (isDisabled) {
      return (
        <div className="flex flex-col items-center">
          <div className="flex items-center justify-center gap-1">
            <IconWarning />

            <Text type="subBody" variant="regular" className="text-Error-Color">
              {t('workflowdetail.connection_is_currently_disabled')}
            </Text>
          </div>
          <Text type="subBody" variant="regular" className="text-Error-Color">
            {t('workflowdetail.please_select_the_enabled_one_to_continue')}
          </Text>
        </div>
      )
    } else if (isExpired && selectedCredentialId) {
      return (
        <div className="flex flex-col items-center">
          <div className="flex items-center justify-center gap-1">
            <IconWarning />

            <Text type="subBody" variant="regular" className="text-Error-Color">
              {t('workflowdetail.token_has_been_expired')}
            </Text>
          </div>

          <Text
            type="subBody"
            variant="semibold"
            className="select-none text-Error-Color decoration-[11%] underline-offset-[25%] hover:cursor-pointer hover:underline"
            onClick={handleReconnect}
          >
            {isReconnecting
              ? t('workflowdetail.re_connecting')
              : t('workflowdetail.reconnect')}
          </Text>
        </div>
      )
    } else if (isError) {
      return (
        <div className="flex flex-col items-center">
          <div className="flex items-center justify-center gap-1">
            <IconWarning />

            <Text type="subBody" variant="regular" className="text-Error-Color">
              {t('workflowdetail.something_wrong_with_connection')}
            </Text>
          </div>
          <Text type="subBody" variant="regular" className="text-Error-Color">
            {t('workflowdetail.please_recheck_connection_details')}
          </Text>
        </div>
      )
    }

    return
  }

  const body = (() => {
    if (loading || initializing) {
      return (
        <div className="flex h-full w-full items-center justify-center">
          <Spin size="larger" />
        </div>
      )
    }

    return (
      <div className="flex h-full w-full gap-[8px] overflow-hidden">
        {(authType === CredentialType.COMPOSIO_OAUTH2API ||
          authType === CredentialType.LINKED_IN_OAUTH2API ||
          authType === CredentialType.MICROSOFT_OUTLOOK_OAUTH2API) && (
          <div className="flex min-w-fit flex-col gap-[4px]">
            <Text
              type="subBody"
              variant="medium"
              className="px-1 text-Primary-Color"
              elementType="div"
            >
              {t('workflowdetail.connection')}
            </Text>
            <div className="flex h-full w-full max-w-[320px] flex-col gap-[12px] rounded-[12px] bg-neutral-100 px-[20px] py-[12px]">
              <div className="flex w-full flex-col gap-[16px] py-[8px]">
                {!credentials?.length && !selectedCredentialId ? (
                  <div className="flex flex-col items-center justify-between gap-[12px]">
                    <Button
                      type="secondary"
                      onClick={handleOpenAuthPopup}
                      leftIcon={
                        <div className="min-h-[20px] min-w-[20px]">
                          {getLogo()}
                        </div>
                      }
                      className="h-[32px] w-fit px-[12px]"
                    >
                      {getText()}
                    </Button>

                    {getError()}
                  </div>
                ) : (
                  <div className="flex flex-col gap-[12px]">
                    <div className="flex flex-col items-center justify-between gap-[12px] py-[8px]">
                      <SelectConnection
                        data={credentials.map((cred) => ({
                          id: cred.id,
                          value: cred.id,
                          name: cred?.name,
                        }))}
                        selected={
                          credentials.find(
                            (cred) => cred.id === selectedCredentialId
                          )
                            ? {
                                id: selectedCredentialId!,
                                value: selectedCredentialId!,
                                name: credentials.find(
                                  (cred) => cred.id === selectedCredentialId
                                )?.name,
                              }
                            : credentialValueDefault
                              ? {
                                  id: credentialValueDefault,
                                  value: credentialValueDefault,
                                  name: credentialValueDefault,
                                }
                              : undefined
                        }
                        itemClassName="!gap-[8px]"
                        icon={<div className="mr-[8px]">{getLogo()}</div>}
                        onChangeSelectedValue={handleChangeSelectedCredential}
                        placeholder={t('workflowdetail.select_connection')}
                        className="w-full"
                        containerClassName="rounded-full"
                      />

                      <div className="flex w-full items-center gap-2">
                        <Button
                          onClick={handleOpenAuthPopup}
                          type="secondary"
                          size="small"
                          className="!h-[32px] !w-full !min-w-[85px]"
                        >
                          {t('workflowdetail.add_new')}
                        </Button>

                        {selectedCredentialId && (
                          <Button
                            type="default"
                            className="!h-[32px] w-full py-[5.5px]"
                            size="small"
                            onClick={handleRemoveCredentials}
                          >
                            {t('workflowdetail.remove')}
                          </Button>
                        )}
                      </div>
                    </div>

                    {getError()}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {(authType === CredentialType.COMPOSIO_APIKEY ||
          authType === CredentialType.COMPOSIO_BEARER_TOKEN) && (
          <div className="flex min-w-fit flex-col gap-[4px] overflow-hidden">
            <Text
              type="subBody"
              variant="medium"
              className="px-1 text-Primary-Color"
              elementType="div"
            >
              {t('workflowdetail.connection')}
            </Text>

            {!credentials?.length && !selectedCredentialId ? (
              <div className="flex h-full min-w-[262px] max-w-[320px] flex-col gap-[12px] overflow-hidden rounded-[12px] bg-neutral-100 px-[20px] py-[12px]">
                <div className="genai-scrollbar max-h-full w-full overflow-y-auto">
                  {expectedParams?.map((param) => (
                    <InputWithSecret
                      maxLength={255}
                      placeholder={param.description}
                      label={param.display_name}
                      key={param.name}
                      withSecret={param.is_secret}
                      value={param.value as string}
                      onChange={(e) => {
                        setExpectedParams((prev) => {
                          const newParams = [...prev]
                          const targetParam = newParams.find(
                            (p) => p.name === param.name
                          )
                          if (targetParam) {
                            targetParam.value = e.target.value
                          }
                          return newParams
                        })
                      }}
                      onBlur={(e) => {
                        setExpectedParams((prev) => {
                          const newParams = [...prev]
                          const targetParam = newParams.find(
                            (p) => p.name === param.name
                          )
                          if (targetParam) {
                            targetParam.value = e.target.value.trim()
                          }
                          return newParams
                        })
                      }}
                    />
                  ))}
                </div>

                <div className="flex w-full flex-col items-center gap-[12px]">
                  <div className="flex w-full items-center justify-end">
                    <Button
                      size="small"
                      type="secondary"
                      className="w-[85px] min-w-[85px]"
                      loading={isLoadingCreate}
                      onClick={createCredentialsKey}
                      disabled={
                        !expectedParams?.every(
                          (param) =>
                            param.value &&
                            (typeof param.value === 'string'
                              ? param.value.trim()
                              : param.value)
                        )
                      }
                    >
                      {t('common.save')}
                    </Button>
                  </div>
                  {getError()}
                </div>
              </div>
            ) : (
              <div className="flex h-full min-w-[262px] max-w-[320px] flex-col gap-[12px] overflow-hidden rounded-[12px] bg-neutral-100 px-[20px] py-[12px]">
                <SelectConnection
                  data={credentials.map((cred) => ({
                    id: cred.id,
                    value: cred.id,
                    name: cred?.name,
                  }))}
                  selected={
                    credentials.find((cred) => cred.id === selectedCredentialId)
                      ? {
                          id: selectedCredentialId!,
                          value: selectedCredentialId!,
                          name: credentials.find(
                            (cred) => cred.id === selectedCredentialId
                          )?.name,
                        }
                      : credentialValueDefault
                        ? {
                            id: credentialValueDefault,
                            value: credentialValueDefault,
                            name: credentialValueDefault,
                          }
                        : undefined
                  }
                  itemClassName="!gap-[8px]"
                  icon={<div className="mr-[8px]">{getLogo()}</div>}
                  onChangeSelectedValue={handleChangeSelectedCredential}
                  placeholder={t('workflowdetail.select_connection')}
                  className="mt-[8px] w-full"
                  containerClassName="rounded-full"
                />

                {addNewConnectionMode ? (
                  <div className="flex w-full flex-col items-center gap-[8px] overflow-hidden">
                    <Text
                      type="body"
                      variant="medium"
                      className="mt-[4px] text-Primary-Color"
                    >
                      {t('workflowdetail.new_connection')}
                    </Text>

                    <div className="flex w-full flex-col items-end gap-[12px] overflow-hidden">
                      <div className="genai-scrollbar max-h-full w-full overflow-y-auto">
                        {expectedParams?.map((param) => (
                          <InputWithSecret
                            maxLength={255}
                            isFullWidth
                            placeholder={param.description}
                            label={param.display_name}
                            key={param.name}
                            withSecret={param.is_secret}
                            value={param.value as string}
                            onChange={(e) => {
                              setExpectedParams((prev) => {
                                const newParams = [...prev]
                                const targetParam = newParams.find(
                                  (p) => p.name === param.name
                                )
                                if (targetParam) {
                                  targetParam.value = e.target.value
                                }
                                return newParams
                              })
                            }}
                            onBlur={(e) => {
                              setExpectedParams((prev) => {
                                const newParams = [...prev]
                                const targetParam = newParams.find(
                                  (p) => p.name === param.name
                                )

                                if (targetParam) {
                                  targetParam.value = e.target.value.trim()
                                }
                                return newParams
                              })
                            }}
                          />
                        ))}
                      </div>
                      <div className="flex w-full flex-col items-center gap-[12px]">
                        <div className="flex w-full items-center justify-end gap-[12px]">
                          <Button
                            size="small"
                            type="default"
                            className="w-[85px] min-w-[85px]"
                            onClick={() => {
                              setExpectedParams((prev) =>
                                prev.map((param) => ({ ...param, value: '' }))
                              )
                              setAddNewConnectionMode(false)
                            }}
                          >
                            {t('common.cancel')}
                          </Button>
                          <Button
                            size="small"
                            type="secondary"
                            className="w-[85px] min-w-[85px]"
                            loading={isLoadingCreate}
                            onClick={createCredentialsKey}
                            disabled={
                              !expectedParams?.every(
                                (param) =>
                                  param.value &&
                                  (typeof param.value === 'string'
                                    ? param.value.trim()
                                    : param.value)
                              )
                            }
                          >
                            {t('common.save')}
                          </Button>
                        </div>

                        {getError()}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="flex w-full flex-col items-center gap-[12px]">
                    <div className="mb-[8px] flex w-full items-center gap-[8px]">
                      <Button
                        size="medium"
                        type="secondary"
                        className="w-full"
                        onClick={async () => {
                          const checkFullConnection =
                            await checkSubscriptionPlan()
                          if (checkFullConnection) {
                            setShowErrorFullConnection(true)
                            return
                          } else {
                            setAddNewConnectionMode(true)
                          }
                        }}
                      >
                        {t('workflowdetail.add_new')}
                      </Button>
                      {selectedCredentialId && (
                        <Button
                          type="default"
                          size="medium"
                          className="w-full"
                          onClick={handleRemoveCredentials}
                        >
                          {t('workflowdetail.remove')}
                        </Button>
                      )}
                    </div>
                    {getError()}
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        <div className="genai-scrollbar flex h-full w-full flex-col gap-[12px] overflow-y-auto rounded-[12px] px-[4px]">
          {!params?.length && !envs?.length && (
            <div className="flex h-full w-full items-center justify-center">
              <EmptyData
                type="01"
                size="small"
                content={t(
                  'workflowdetail.no_parameters_added_to_your_code_function'
                )}
              />
            </div>
          )}
          {!!params?.length && (
            <div className="flex w-full flex-grow flex-col gap-[4px]">
              <Text
                type="subBody"
                variant="medium"
                className="px-1 text-Primary-Color"
                elementType="div"
              >
                {t('workflowdetail.input_parameters')}
              </Text>

              <div className="flex w-full flex-grow flex-col gap-[8px] rounded-[12px] bg-neutral-100 px-[16px] py-[12px]">
                <div className="flex w-full items-center gap-[8px]">
                  <div className="w-[400px] min-w-[400px]">
                    <Text
                      type="subBody"
                      variant="medium"
                      value={t('workflowdetail.parameter_name')}
                      className="!w-fit max-w-full overflow-hidden text-Tertiary-Color"
                      elementType="div"
                      ellipsis
                    />
                  </div>

                  <div className="flex w-full items-center gap-[4px]">
                    <Text
                      type="subBody"
                      variant="medium"
                      value={t('workflowdetail.fixed_value')}
                      className="!w-fit max-w-full overflow-hidden text-Tertiary-Color"
                      elementType="div"
                      ellipsis
                    />
                    <IconButton
                      nameIcon="Bold-EssentionalUI-InfoCircle"
                      colorIcon={colors['Tertiary-Color']}
                      hoverColor={colors['Primary-Color']}
                      sizeIcon={14}
                      tooltipText="When fixed value is set, tool is executed with this value. Else, AI determines value by analyzing chat history, the results from the previous step"
                    />
                  </div>
                </div>

                {params
                  .filter(
                    (param) =>
                      param.name !== '___credential_id_' &&
                      param.name !== '___prompt_'
                  )
                  .map((param, index) => (
                    <div key={index} className="flex w-full gap-[8px]">
                      <div className="flex w-[400px] min-w-[400px] flex-col">
                        <Text
                          type="body"
                          variant="medium"
                          value={param.name}
                          className="!w-fit max-w-full overflow-hidden text-Primary-Color"
                          elementType="div"
                          ellipsis
                        />

                        {param.description && (
                          <Text
                            type="subBody"
                            variant="regular"
                            value={param.description}
                            className="!w-fit max-w-full overflow-hidden text-Tertiary-Color"
                            elementType="div"
                            ellipsis
                          />
                        )}
                      </div>

                      <InputByType
                        value={param.default_value ?? ''}
                        type={param.type as EParamType}
                        onChange={(val) => {
                          const newParams = [...params]

                          if (param.type === 'bool') {
                            newParams[index].default_value =
                              val === params[index].default_value ? '' : val
                          } else {
                            newParams[index].default_value = val as string
                          }
                          setParams(newParams)
                        }}
                        placeholder={t(
                          'workflowdetail.enter_fixed_value_for_parameter'
                        )}
                        enumInput={param.enum}
                      />
                    </div>
                  ))}
              </div>
            </div>
          )}
          {!!envs?.length && (
            <div className="flex w-full flex-grow flex-col gap-[4px]">
              <Text
                type="subBody"
                variant="medium"
                className="px-1 text-Primary-Color"
                elementType="div"
              >
                {t('workflowdetail.environment_variables')}
              </Text>

              <div className="flex w-full flex-grow flex-col gap-[8px] rounded-[12px] bg-neutral-100 px-[16px] py-[12px]">
                <div className="flex w-full items-center gap-[8px]">
                  <div className="w-[400px] min-w-[400px]">
                    <Text
                      type="subBody"
                      variant="medium"
                      value={t('workflowdetail.variable_name')}
                      className="!w-fit max-w-full overflow-hidden text-Tertiary-Color"
                      elementType="div"
                      ellipsis
                    />
                  </div>

                  <div className="flex w-full items-center gap-[4px]">
                    <Text
                      type="subBody"
                      variant="medium"
                      value={t('workflowdetail.fixed_value')}
                      className="!w-fit max-w-full overflow-hidden text-Tertiary-Color"
                      elementType="div"
                      ellipsis
                    />
                  </div>
                </div>

                {envs.map((val, index) => (
                  <div
                    key={index}
                    className="flex w-full items-center gap-[8px]"
                  >
                    <div className="flex w-[400px] min-w-[400px] flex-col">
                      <Text
                        type="body"
                        variant="medium"
                        value={val.name}
                        className="!w-fit max-w-full overflow-hidden text-Primary-Color"
                        elementType="div"
                        ellipsis
                      />

                      {val.description && (
                        <Text
                          type="subBody"
                          variant="regular"
                          value={val.description}
                          className="!w-fit max-w-full overflow-hidden text-Tertiary-Color"
                          elementType="div"
                          ellipsis
                        />
                      )}
                    </div>

                    <Input
                      placeholder={t(
                        'workflowdetail.enter_fixed_value_for_variable'
                      )}
                      maxLength={3000}
                      classNameInputWrapper="w-full"
                      value={val.default_value || ''}
                      onChange={(e) => {
                        const newEnvs = [...envs]
                        newEnvs[index].default_value = e.target.value
                        setEnvs(newEnvs)
                      }}
                      onBlur={() => {
                        const newEnvs = [...envs]
                        newEnvs[index].default_value = val.default_value?.trim()
                        setEnvs(newEnvs)
                      }}
                      suffix={
                        val.is_secret && (
                          <IconButton
                            nameIcon={
                              val.feIsSecret
                                ? 'vuesax-outline-eye'
                                : 'vuesax-outline-eye-slash'
                            }
                            colorIcon={colors['neutral']['300']}
                            hoverColor={colors['Primary-Color']}
                            sizeIcon={16}
                            className="select-none"
                            onClick={() => {
                              const newEnvs = [...envs]
                              newEnvs[index].feIsSecret = !val.feIsSecret
                              setEnvs(newEnvs)
                            }}
                          />
                        )
                      }
                      secure={!val.feIsSecret}
                    />
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    )
  })()

  return (
    <BaseModal isOpen onClose={onClose} isPureModal>
      <div className="relative flex h-[600px] w-[1124px] flex-col items-end gap-[12px] rounded-[20px] bg-white p-3 shadow-md">
        <div className="flex h-full w-full flex-col gap-[16px] overflow-hidden">
          <Text
            type="subheading"
            variant="medium"
            className="px-1 text-Primary-Color"
            elementType="div"
          >
            {t('workflowdetail.tool_parameters')}
          </Text>

          <div className="flex h-full w-full flex-col overflow-hidden px-[4px]">
            {body}
            {authType && !authType.includes('Composio') && (
              <>
                <div className="mb-[8px] mt-[16px] h-[1px] min-h-[1px] w-full bg-neutral-200"></div>
                <div className="flex flex-col gap-[4px]">
                  <Text
                    type="subBody"
                    variant="medium"
                    className="ml-[4px] text-Primary-Color"
                  >
                    {t('common.when_to_use')}
                  </Text>
                  <TextArea
                    placeholder={t(
                      'workflowdetail.describe_when_the_tool_will_be_executed'
                    )}
                    className="h-[84px] w-full"
                    value={whenToUse}
                    isError={isErrorPrompt}
                    onChange={(e) => {
                      setWhenToUse(e)
                      const newParams = [...params]
                      const promptParam = newParams.find(
                        (p) => p.name === '___prompt_'
                      )
                      if (promptParam) {
                        promptParam.default_value = e
                        setParams(newParams)
                      }
                    }}
                  />
                </div>
              </>
            )}
          </div>
        </div>
        <Button
          type="primary"
          className="mr-[12px] w-fit"
          disabled={
            loading ||
            initializing ||
            (!params?.length && !envs?.length) ||
            (!!authType &&
              authType !== CredentialType.COMPOSIO_NO_AUTH &&
              ((!whenToUse && !authType.includes('Composio')) ||
                !params.find((p) => p.name === '___credential_id_')
                  ?.default_value)) ||
            isDisabled
          }
          onClick={() => {
            // Ensure ___prompt_ exists for non-GENERAL tools
            const finalParams = [...params]
            if (authType && authType !== CredentialType.COMPOSIO_NO_AUTH) {
              if (!authType.includes('Composio')) {
                const promptParam = finalParams.find(
                  (p) => p.name === '___prompt_'
                )

                if (!promptParam) {
                  finalParams.push({
                    name: '___prompt_',
                    type: 'str',
                    default_value: whenToUse,
                    description:
                      'Hidden parameter to store when to use description',
                  })

                  if (!whenToUse) {
                    Message.error({
                      message: t(
                        'workflowdetail.missing_or_invalid_tool_parameters'
                      ),
                    })
                    setIsErrorPrompt(true)
                    return
                  }
                } else {
                  if (!promptParam.default_value) {
                    Message.error({
                      message: t(
                        'workflowdetail.missing_or_invalid_tool_parameters'
                      ),
                    })
                    setIsErrorPrompt(true)
                    return
                  }
                }
              }
              const connectionParam = finalParams.find(
                (p) => p.name === '___credential_id_'
              )

              if (!connectionParam?.default_value) {
                Message.error({
                  message: t(
                    'workflowdetail.missing_or_invalid_tool_parameters'
                  ),
                })
                setIsErrorPrompt(false)

                return
              }
            }

            onSave?.({
              inputParameters: finalParams
                .filter((param) => {
                  if (param?.default_value === '') {
                    return false
                  }
                  return !isNil(param?.default_value)
                })
                .map((param) => ({
                  name: param.name,
                  value: param.default_value,
                })),
              environmentVariables: envs
                .filter((env) => env?.default_value)
                .map((env) => ({
                  name: env.name,
                  value: env?.default_value,
                })),
            })
          }}
          size="small"
        >
          {t('common.save')}
        </Button>
        <IconButton
          className="absolute right-[10px] top-[10px] z-30 border-0 bg-transparent p-0 duration-300 hover:bg-neutral-200"
          nameIcon="x-close"
          sizeIcon={16}
          colorIcon={colors.neutral[500]}
          onClick={onClose}
        />
      </div>
    </BaseModal>
  )
}

interface IModalGeneralToolParameters {
  onClose: () => void
  onSave: ({
    inputParameters,
    environmentVariables,
  }: GeneralToolParamConfigurationData) => void
  toolId: string
  generalToolParamConfigurations?: GeneralToolParamConfigurationData
  authType: string | null | undefined
  logo?: string | null | undefined
  groupId?: string | null | undefined
}

export default memo(ModalGeneralToolParameters)
