import Icon from '@/assets/icon/Icon'
import Text from '@/components/Text'
import { getUrlImage } from '@/helpers'
import { isEmpty } from 'lodash'
import { useEffect, useMemo, useState } from 'react'

const bgColors = [
  'bg-slate-50',
  'bg-gray-50',
  'bg-zinc-50',
  'bg-neutral-50',
  'bg-stone-50',
  'bg-red-50',
  'bg-orange-50',
  'bg-amber-50',
  'bg-yellow-50',
  'bg-lime-50',
  'bg-green-50',
  'bg-emerald-50',
  'bg-teal-50',
  'bg-cyan-50',
  'bg-sky-50',
  'bg-blue-50',
  'bg-indigo-50',
  'bg-violet-50',
  'bg-purple-50',
  'bg-fuchsia-50',
  'bg-pink-50',
  'bg-rose-50',
]

enum LoadImage {
  none = 'none',
  success = 'success',
  error = 'error',
}

const CategoryItem = ({
  image,
  number,
  title,
  onDelete,
  onClick,
  hideDelete,
}: CategoryProps) => {
  const [isHover, setIsHover] = useState(false)
  const [isHoverDelete, setIsHoverDelete] = useState(false)
  const [loadImageState, setLoadImageState] = useState<LoadImage>(
    LoadImage.none
  )

  const handleImageLoad = () => {
    setLoadImageState(LoadImage.success)
  }

  const handleImageError = () => {
    setLoadImageState(LoadImage.error)
  }

  const iconBg = useMemo(() => {
    if (!image || loadImageState === LoadImage.error) {
      return 'bg-Background-Color'
    }
    const randomIndex = Math.floor(Math.random() * bgColors.length)
    return bgColors[randomIndex]
  }, [image, loadImageState])

  useEffect(() => {
    if (!image) {
      setLoadImageState(LoadImage.error)
    } else {
      setLoadImageState(LoadImage.none)
    }
  }, [image])

  const displayNumber = useMemo(() => {
    if (number < 2) {
      return `${number} tool`
    }
    return `${number} tools`
  }, [number])

  return (
    <div
      onClick={onClick}
      className="relative flex h-[120px] w-[205px] flex-col items-center rounded-[16px] bg-white p-[16px] pt-[8px] shadow hover:bg-Hover-Color"
      onMouseEnter={() => setIsHover(true)}
      onMouseLeave={() => setIsHover(false)}
    >
      <div
        className={`flex h-[48px] w-[48px] flex-col items-center justify-center rounded-[24px] ${iconBg}`}
      >
        {loadImageState !== LoadImage.error && !isEmpty(image) ? (
          <img
            src={getUrlImage(image)}
            alt="img"
            className="h-[32px] w-[32px] rounded-full"
            onLoad={handleImageLoad}
            onError={handleImageError}
          />
        ) : (
          <Icon
            name="layers-two-01"
            size={32}
            gradient={['#642B734D', '#C6426E4D']}
          />
        )}
      </div>
      <Text
        type="body"
        variant="semibold"
        className="text-primary mt-[8px] !w-fit max-w-[173px] text-center"
        elementType="div"
        tooltipPosition="left"
        ellipsis
      >
        {title}
      </Text>
      <Text
        type="supportText"
        variant="regular"
        className="mt-[4px] text-Secondary-Color"
        elementType="div"
      >
        {displayNumber}
      </Text>
      {!hideDelete && isHover && (
        <button
          onMouseEnter={() => setIsHoverDelete(true)}
          onMouseLeave={() => setIsHoverDelete(false)}
          className="absolute right-[8px] top-[8px] flex h-[24px] w-[24px] cursor-pointer items-center justify-center"
          onClick={(e) => {
            e.stopPropagation()
            onDelete()
          }}
        >
          {isHoverDelete ? (
            <Icon
              name="Customize-Delete"
              size={24}
              gradient={['#642B73', '#C6426E']}
            />
          ) : (
            <Icon name="Customize-Delete" size={24} color="#E5E5E5" />
          )}
        </button>
      )}
      {loadImageState === LoadImage.none && (
        <div className="right-0, absolute bottom-0 left-0 top-0 flex h-[120px] w-[205px] flex-col rounded-[16px] bg-white p-[16px] pt-[8px]">
          <div className="flex animate-pulse flex-col items-center">
            <div className="h-[48px] w-[48px] rounded-[24px] bg-slate-200"></div>
            <div className="mt-[8px] h-[20px] w-[163px] rounded bg-slate-200"></div>
            <div className="mt-[4px] h-[15px] w-[80px] rounded bg-slate-200"></div>
          </div>
        </div>
      )}
    </div>
  )
}

export default CategoryItem

export interface CategoryProps {
  image: string | null
  title: string
  number: number
  onDelete: () => void
  onClick: () => void
  hideDelete?: boolean
}
