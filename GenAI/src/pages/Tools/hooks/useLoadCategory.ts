import {
  ToolCategoryPublic,
  toolCategoriesReadToolCategoriesApi,
} from '@/apis/client'
import {
  PAGE_SIZE_LIST_CATEGORY,
  UNDEFINED_CATEGORY_ID,
} from '@/pages/toolMarketplace/components/ListCategory/consts'
import { useEffect, useState } from 'react'

let timeout: ReturnType<typeof setTimeout> | null
const DEFAULT_PAGINATION = {
  total_count: 0,
  next_page: null,
  prev_page: null,
  total_page: 0,
}

export const useLoadCategory = (initInputValue: string = '') => {
  const [isLoading, setIsLoading] = useState(false)
  const [inputValue, setInputValue] = useState(initInputValue)
  const [pagination, setPagination] = useState<{
    total_count: number
    total_page: number
    next_page: number | null
    prev_page: number | null
  }>(DEFAULT_PAGINATION)
  const [currentPage, setCurrentPage] = useState(1)

  const [data, setData] = useState<ToolCategoryPublic[]>([])

  const onChangeInputValue = (newValue: string) => {
    setCurrentPage(1)
    setInputValue(newValue)
  }

  const loadCategory = async (page: number) => {
    try {
      setIsLoading(true)

      const { data } = await toolCategoriesReadToolCategoriesApi({
        query: {
          name: inputValue,
          page_number: page,
          page_size: PAGE_SIZE_LIST_CATEGORY,
          exclude_built_in: true,
        },
      })

      if (data && data.data && data.data.data) {
        setPagination({
          total_count: data.data.total_count,
          total_page: data.data.total_pages,
          next_page: data.data.next_page,
          prev_page: data.data.prev_page,
        })

        //remove undefined category
        const newData = data.data.data.filter(
          (item: any) => item.id !== UNDEFINED_CATEGORY_ID
        )

        if (page === 1) {
          setData(newData)
        } else {
          setData((d) => [...d, ...newData])
        }
      }
    } catch (error) {
      console.log('🚀 ~ loadCategory ~ error:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    if (timeout) {
      clearTimeout(timeout)
    }

    timeout = setTimeout(() => {
      loadCategory(currentPage)
    }, 500)
  }, [currentPage, inputValue])

  const handleReachEnd = () => {
    if (pagination.next_page) {
      setCurrentPage(pagination.next_page)
    }
  }

  const reLoadCategory = () => {
    if (currentPage === 1) {
      loadCategory(1)
    } else {
      setCurrentPage(1)
    }
  }

  return {
    inputValue,
    isLoading,
    data,
    onChangeInputValue,
    handleReachEnd,
    reLoadCategory,
  }
}
