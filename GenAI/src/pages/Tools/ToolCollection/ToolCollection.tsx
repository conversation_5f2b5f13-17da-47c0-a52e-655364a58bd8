import { toolCategoriesReadToolCategoriesApi } from '@/apis/client'
import Layout from '@/components/Layout'
import PageHeader from '@/components/PageHeader'
import SearchBar from '@/components/SearchBar'
import { ISelectBaseItem } from '@/components/Select'
import { HTTP_STATUS_CODE, PAGE_SIZE } from '@/constants'
import ToolPurchased from '@/pages/Tools/ToolCollection/ToolPurchased'
import { memo, useCallback, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import i18n from '@/i18n'

const OptionAllCategories = {
  id: 'all',
  name: i18n.t('common.category'),
}

const ToolCollection = () => {
  const { t } = useTranslation()
  const [searchKey, setSearchKey] = useState('')
  const [categories, setCategories] = useState<ISelectBaseItem[]>([])
  const [selectedCategory, setSelectedCategory] = useState<ISelectBaseItem[]>([
    OptionAllCategories,
  ])
  const [loading, setLoading] = useState(false)

  const fetchCategory = useCallback(
    async (page: number) => {
      try {
        const res = await toolCategoriesReadToolCategoriesApi({
          query: {
            page_number: page,
            page_size: PAGE_SIZE.FULL,
            exclude_built_in: true,
          },
        })

        if (res.status === HTTP_STATUS_CODE.SUCCESS) {
          const { data } = res

          setCategories((pre) => {
            const filterDuplicate = data?.data?.data.filter(
              (item: any) => !pre.find((category) => category.id === item.id)
            )
            return [
              ...pre,
              ...(filterDuplicate || []).map((item) => ({
                id: item.id,
                name: item.name,
              })),
            ]
          })
          if (data?.data?.total_pages && page < data?.data?.total_pages) {
            fetchCategory(page + 1)
          } else {
            setLoading(false)
          }
        } else {
          setLoading(false)
        }
      } catch (error) {
        setLoading(false)
      }
    },
    [categories]
  )

  const fetchAllCategories = useCallback(() => {
    setLoading(true)
    setCategories([])
    fetchCategory(1)
  }, [])

  const selectedCategoryIds = useMemo(
    () =>
      selectedCategory
        .filter((item) => item.id !== OptionAllCategories.id)
        .map((item) => item.id),
    [selectedCategory]
  )

  return (
    <Layout>
      <div className="tool-collection flex h-full flex-col">
        <div className="flex items-center justify-between">
          <PageHeader
            breadcrumbPaths={[
              {
                name: t('tool_collection.tools'),
              },
            ]}
            title={t('tool_collection.the_backbone_of_worker')}
          />

          <SearchBar
            onSearch={(value) => {
              setSearchKey(value)
            }}
            hasFilter
            filterOptions={[
              {
                data: categories.filter(
                  (item) => item.id !== OptionAllCategories.id
                ),
                onChangeSelectedValue: (value) => {
                  const isSelected = selectedCategory.find(
                    (item) => item.id === value.id
                  )
                  if (isSelected) {
                    const newVal = selectedCategory.filter(
                      (item) => item.id !== value.id
                    )

                    if (newVal.length === 0) {
                      setSelectedCategory([OptionAllCategories])
                    } else {
                      setSelectedCategory(newVal)
                    }
                  } else {
                    setSelectedCategory(
                      [...selectedCategory, value].filter(
                        (item) => item.id !== OptionAllCategories.id
                      )
                    )
                  }
                },
                selected: selectedCategory[0],
                selectedMultiple: selectedCategory,
                multipleChoice: true,
                allowClear: selectedCategory[0].id !== OptionAllCategories.id,

                onClear: () => {
                  if (selectedCategory[0].id !== OptionAllCategories.id) {
                    setSelectedCategory([OptionAllCategories])
                  }
                },
                onOpen: fetchAllCategories,
                loading,
                optionMaxWidth: 320,
                contentClassName: '!w-full',
                overlayClassName: '[--anchor-gap:12px] ml-3',
              },
            ]}
          />
        </div>

        <ToolPurchased searchKey={searchKey} categories={selectedCategoryIds} />
      </div>
    </Layout>
  )
}

export default memo(ToolCollection)
