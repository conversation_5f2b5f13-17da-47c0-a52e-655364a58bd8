import i18n from '@/i18n'
import { DATA_TYPES } from '@/pages/WorkflowDetail/components/PlaygroundAutomationProcess/AddFileProcess/helper'

export interface ILocalFile {
  id: string
  name: string
  size: number
  type: string
  file: File
  error?: string
}

export interface ILocalMessageParams {
  text?: string
  files?: ILocalFile[]
  intent?: string
}

export const ArrAIOperation = [
  { id: 'Summarize content', name: i18n.t('intent.summarize_content') },
  { id: 'Extract information', name: i18n.t('intent.extract_information') },
  { id: 'Query and search', name: i18n.t('intent.query_and_search') },
]

export const getErrorValidateFileWithMax = (
  size: number,
  type: string,
  maxMB: number
) => {
  if (!DATA_TYPES?.includes(type)) {
    return i18n.t('common.not_supported')
  }
  const maxSize = maxMB * 1048576

  if (size > maxSize) return i18n.t('home.file_max_size', { size: maxMB })
  return ''
}
