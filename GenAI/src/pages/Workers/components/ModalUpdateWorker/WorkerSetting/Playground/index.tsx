import { usersReadCurrentUser } from '@/apis/client'
import Icon from '@/assets/icon/Icon'
import { getAccessTokenLocalStorage, getPlaygroundUrl } from '@/helpers'
import { colors } from '@/theme'
import { nanoid } from 'nanoid'
import { memo, useEffect, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { v4 as uuid } from 'uuid'
import Avatar from '../../../../../../components/Avatar'
import Text from '../../../../../../components/Text'
import TextArea from '../../../../../../components/TextArea'
import BotMessage from './components/BotMessage'
import HelloMessage from './components/HelloMessage'
import Responding from './components/Responding'
import UserMessage from './components/UserMessage'
import {
  EMessageType,
  IMessage,
  LETS_START_MESSAGE,
  LETS_START_RESPONSE,
  MAX_LENGTH,
  getTimeChat,
} from './helpers'

const Playground = ({
  avatar,
  name,
  playgroundId,
  workerType,
  personality,
  communicationStyle,
  language,
}: IPlaygroundProps) => {
  const { t } = useTranslation()
  const [message, setMessage] = useState<IMessage[]>([])
  const [text, setText] = useState('')
  const [responding, setResponding] = useState(false)
  const [responseMessage, setResponseMessage] = useState('')
  const [responseTime, setResponseTime] = useState('')
  const socket = useRef<WebSocket | null>(null)
  const [isSocketReady, setSocketReady] = useState(false)

  const mounted = useRef(true)
  const [sessionId, setSessionId] = useState<string>(uuid())
  const timeout = useRef<NodeJS.Timeout | null>(null)

  const connectToPlayground = async () => {
    if (timeout.current) {
      clearTimeout(timeout.current)
    }
    const token = getAccessTokenLocalStorage()
    if (socket.current) {
      socket.current.close()
      socket.current = null
    }
    setResponding(false)
    setResponseMessage('')
    setResponseTime('')
    if (!playgroundId) return
    const tSocket = new WebSocket(
      getPlaygroundUrl(playgroundId, token || '', sessionId)
    )
    socket.current = tSocket
  }

  useEffect(() => {
    if (!socket.current) return
    socket.current.onopen = () => {
      console.log('Connected to playground')
      setSocketReady(true)
    }
    socket.current.onmessage = (event) => {
      const res = JSON.parse(event.data) as IMessage
      if (res.type === EMessageType.worker) {
        if (!responseTime) {
          setResponseTime(getTimeChat())
        }
        setResponseMessage((prev) => prev + res.text)
      } else if (res.type === EMessageType.end) {
        setResponding(false)
        setMessage([
          {
            id: nanoid(),
            text: responseMessage,
            type: EMessageType.worker,
            time: responseTime,
          },
          ...message,
        ])
        setResponseMessage('')
        setResponseTime('')
      }
    }
    socket.current.onclose = (e) => {
      console.log('Onclose to playground', e)
      if (e.code === 3401) {
        usersReadCurrentUser()
      }
      if (mounted.current) {
        timeout.current = setTimeout(() => {
          connectToPlayground()
        }, 1000)
      }

      setSocketReady(false)
      socket.current = null
    }
  }, [socket.current, message, responseMessage])

  useEffect(() => {
    connectToPlayground()
    setMessage([])
  }, [playgroundId])

  useEffect(() => {
    setMessage([])
    return () => {
      socket.current?.close()
      mounted.current = false
    }
  }, [])

  const props = useMemo(() => {
    if (text.trimStart().trimEnd().length === 0 || responding) {
      return { color: colors['Base-Neutral'] }
    }
    return { gradient: ['#642B73', '#C6426E'] }
  }, [text, responding])

  const className = useMemo(() => {
    if (text.length === 0 || responding) {
      return 'h-[20px] w-[20px] rounded border-0 bg-transparent flex items-center justify-center'
    }
    return 'flex items-center justify-center h-[20px] w-[20px] rounded border-0 hover:bg-Hover-2'
  }, [text, responding])

  const resetHandler = () => {
    setSessionId(uuid())
    setMessage([])
    setText('')
  }

  const chatHandler = (tVal: string) => {
    const val = tVal.trimStart().trimEnd()
    if (val.length === 0 || responding || !isSocketReady) return
    setResponding(true)
    setText('')
    setMessage((pre) => [
      {
        id: nanoid(),
        text: val,
        type: EMessageType.user,
        time: getTimeChat(),
      },
      ...pre,
    ])
    if (val.toLocaleLowerCase() === LETS_START_MESSAGE) {
      setTimeout(() => {
        setMessage((pre) => [
          {
            id: nanoid(),
            text: LETS_START_RESPONSE,
            type: EMessageType.worker,
            time: getTimeChat(),
            hideCopy: true,
          },
          ...pre,
        ])
        setResponding(false)
      }, 1000)
    } else {
      socket.current?.send(
        JSON.stringify({
          text: val,
          type: EMessageType.user,
          chat_history: message
            .filter(
              ({ text, type }) =>
                (type === EMessageType.user &&
                  text?.toLocaleLowerCase() !== LETS_START_MESSAGE) ||
                (type === EMessageType.worker && text !== LETS_START_RESPONSE)
            )
            .map(({ text, type }) => ({
              text,
              type,
            }))
            .reverse(),
        })
      )
    }
  }

  const displayMessage = useMemo(() => {
    if (responding) {
      if (!responseMessage) {
        return [
          {
            text: '',
            type: EMessageType.loading,
            time: getTimeChat(),
          },
          ...message,
        ]
      }
      return [
        {
          text: responseMessage,
          type: EMessageType.worker,
          time: getTimeChat(),
          loading: true,
        },
        ...message,
      ]
    }
    return message
  }, [message, responding, responseMessage])

  return (
    <div className="flex w-full flex-1 flex-col overflow-hidden rounded-[12px] bg-white p-[16px]">
      <div className="!h-8 !w-8 self-center">
        <Avatar
          avatarUrl={avatar}
          avatarDefault={
            <div className="flex size-10 items-center justify-center rounded-full bg-Background-Color">
              <Icon
                name={
                  workerType === 'Human Worker'
                    ? 'Outline-FacesEmotionsStickers-ExpressionlessCircle'
                    : 'face-id-square'
                }
                size={28}
                gradient={['#642B734D', '#C6426E4D']}
              />
            </div>
          }
        />
      </div>
      <Text
        type="subBody"
        variant="medium"
        elementType="div"
        className="mt-[8px] text-center text-Primary-Color"
      >
        {t('workers.having_task_let_name_take_care', { name })}
      </Text>
      <div className="genai-scrollbar flex h-full w-full flex-col-reverse gap-1 overflow-y-auto">
        {displayMessage.map((item) => {
          if (item.type === 'loading') {
            return (
              <Responding
                avatar={avatar}
                key={item.id}
                workerType={workerType}
              />
            )
          }
          if (item.type === EMessageType.user) {
            return (
              <UserMessage message={item.text} time={item.time} key={item.id} />
            )
          }
          return (
            <BotMessage
              avatar={avatar}
              message={item.text}
              time={item.time}
              key={item.id}
              loading={item.loading}
              hideCopy={item.hideCopy}
              workerType={workerType}
            />
          )
        })}
      </div>
      {message.length === 0 && (
        <HelloMessage
          name={name}
          personality={personality}
          communicationStyle={communicationStyle}
          language={language}
        />
      )}
      <div className="mb-[4px] mt-[12px] flex justify-end pr-[12px]">
        <button
          className="flex gap-[4px] rounded border-0 bg-transparent pb-[2px] pl-[4px] pr-[4px] pt-[2px] hover:bg-Hover-Color"
          onClick={resetHandler}
          disabled={responding}
        >
          <Icon
            name="Outline-MessagesConversation-Dialog"
            size={16}
            color={colors['Primary-Color']}
          />
          <Text
            type="supportText"
            variant="medium"
            className="text-Primary-Color"
          >
            {t('workers.new_chat')}
          </Text>
        </button>
      </div>
      <TextArea
        placeholder={t('workers.send_lets_start_to_begin')}
        className="ml-[12px] mr-[12px] h-[unset] max-h-[128px] min-h-[80px] text-Primary-Color"
        maxLength={MAX_LENGTH}
        maxHeight={90}
        autoResize
        onChange={setText}
        value={text}
        onSend={chatHandler}
        bottomChild={
          <div className="mt-[4px] flex justify-end">
            <button
              className={className}
              onClick={() => chatHandler(text)}
              disabled={text.trimStart().trimEnd().length === 0 || responding}
            >
              <Icon name="vuesax-bold-send" size={16} {...props} />
            </button>
          </div>
        }
      />
    </div>
  )
}

export default memo(Playground)

interface IPlaygroundProps {
  name: string
  avatar: string
  playgroundId: string
  workerType: string
  personality: string
  language?: string
  communicationStyle?: string
}
