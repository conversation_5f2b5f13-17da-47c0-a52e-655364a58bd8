import { memo, useCallback, useContext, useMemo, useState } from 'react'

import { WorkerLanguagePublic, WorkerPublic } from '@/apis/client'
import Icon from '@/assets/icon/Icon'
import Avatar from '@/components/Avatar'
import { MessageDialog } from '@/components/DialogMessage'
import Dropdown, { SelectedItemProps } from '@/components/Dropdown'
import More from '@/components/More'
import Text from '@/components/Text'
import { LanguageContext, ModelsContext } from '@/pages/Workers/contexts'
import clsx from 'clsx'
import { WorkerActions, WorkerType, workerActionItems } from '../../types'
import styles from './styles.module.scss'
import { getUrlImage } from '@/helpers'
import IconBuiltInTool from '@/components/IconBuiltInTool'
import { useMyProfile } from '@/hooks/useMyProfile'
import { useTranslation } from 'react-i18next'

export interface WorkerActionProps {
  workerId?: string
}

const WorkerItem = ({
  workerId,
  name,
  background,
  avatar,
  language,
  onClick,
  onDeleteWorker,
  llm_model_id,
  isShowAction = true,
  worker_type = 'AI Worker',
  onPlayground,
  onDuplicateWorker,
  onAddSystemTag,
  onRemoveSystemTag,
  system_worker,
}: Partial<WorkerPublic> & {
  onClick: () => void
  onDeleteWorker?: ({ workerId }: WorkerActionProps) => void
  onDuplicateWorker?: ({ workerId }: WorkerActionProps) => void
  onRemoveSystemTag?: ({ workerId }: WorkerActionProps) => void
  workerId: string
  isShowAction?: boolean
  onPlayground: () => void
  onAddSystemTag: () => void
  system_worker: boolean | undefined
}) => {
  const { t } = useTranslation()
  const { languages } = useContext(LanguageContext)
  const [openWorkerAction, setOpenWorkerAction] = useState(false)
  const { models } = useContext(ModelsContext)

  const { isSuperAdmin } = useMyProfile()

  const getAvatarModel = useCallback(
    (id: string | undefined | null) => {
      if (id) {
        const model = models.find((n) => n.id === id)

        return model?.image_url
      }
      return
    },
    [models]
  )

  const getNameModel = useCallback(
    (id: string | undefined | null) => {
      if (id) {
        const model = models.find((n) => n.id === id)

        return model?.display_name
      }
      return
    },
    [models]
  )
  const languageFlag = useMemo(() => {
    if (!language) return ''
    return (
      languages.find((lang: WorkerLanguagePublic) => lang.name === language)
        ?.national_flag ?? ''
    )
  }, [languages, language])

  const handleDeleteWorker = useCallback(() => {
    MessageDialog.warning({
      mainMessage: t('workers.delete_worker'),
      subMessage: t(
        'workers.if_continue_this_worker_will_be_permanently_deleted'
      ),
      onClick: () => onDeleteWorker?.({ workerId }),
    })
  }, [workerId, onDeleteWorker])

  const handleSelectWorkerAction = useCallback(
    ({ key }: SelectedItemProps) => {
      switch (key) {
        case WorkerActions.PLAYGROUND:
          onPlayground()
          break
        case WorkerActions.DELETE:
          handleDeleteWorker()
          break
        case WorkerActions.DUPLICATE:
          onDuplicateWorker?.({ workerId })
          break
        case WorkerActions.ADD_SYSTEM_TAG:
          onAddSystemTag()
          break
        case WorkerActions.REMOVE_SYSTEM_TAG:
          onRemoveSystemTag?.({ workerId })
          break
      }
    },
    [
      handleDeleteWorker,
      onDuplicateWorker,
      onAddSystemTag,
      onRemoveSystemTag,
      workerId,
    ]
  )

  const _WorkerMoreOptions = useMemo(() => {
    const options = [...workerActionItems]

    if (isSuperAdmin) {
      if (!system_worker) {
        options.push({
          key: WorkerActions.ADD_SYSTEM_TAG,
          label: t('workers.add_system_tag'),
        })
      } else {
        options.push({
          key: WorkerActions.REMOVE_SYSTEM_TAG,
          label: t('workers.remove_system_tag'),
        })
      }
    }

    return options
  }, [isSuperAdmin, system_worker, t])

  return (
    <div className={styles['worker-item']} onClick={onClick}>
      {isShowAction && (
        <div className="absolute right-[12px] top-[12px] flex flex-row-reverse gap-1">
          <Dropdown
            // overlayClassName="w-[113px]"
            open={openWorkerAction}
            items={_WorkerMoreOptions}
            onSelect={handleSelectWorkerAction}
            onOpenChange={(newOpen) => {
              setOpenWorkerAction(newOpen)
            }}
          >
            <More active={openWorkerAction} />
          </Dropdown>
        </div>
      )}

      {system_worker && (
        <div className="absolute left-[16px] top-[16px] flex flex-row-reverse gap-1">
          <IconBuiltInTool />
        </div>
      )}

      <Avatar
        name={name}
        avatarUrl={avatar!}
        size="large"
        avatarDefault={
          <div className="flex size-[42px] items-center justify-center rounded-full bg-Background-Color">
            <Icon
              name={
                worker_type === WorkerType.HUMAN
                  ? 'Outline-FacesEmotionsStickers-ExpressionlessCircle'
                  : 'face-id-square'
              }
              size={28}
              gradient={['#642B734D', '#C6426E4D']}
            />
          </div>
        }
      />
      <Text
        variant="medium"
        className="mb-[4px] mt-[12px] w-full text-center text-Primary-Color"
        value={name}
        elementType="div"
        ellipsis
      />
      <Text
        className={clsx(styles['worker-description'], 'h-[74px]')}
        value={background}
        type="subBody"
        elementType="div"
        ellipsis
        multipleLine={4}
      />
      <div className="my-[8px] h-[2px] min-h-[2px] w-full bg-neutral-100"></div>
      <div className="my-[4px] flex w-full items-center justify-between gap-1">
        <div className="flex items-center justify-center gap-1">
          <div
            style={{
              backgroundImage: `url(${
                getAvatarModel(llm_model_id)
                  ? getUrlImage(getAvatarModel(llm_model_id))
                  : ''
              })`,
              backgroundRepeat: 'no-repeat',
              backgroundSize: '20px',
              backgroundPosition: 'center',
            }}
            className="size-[20px] min-w-[20px] items-center justify-center rounded-full"
          />

          <Text
            value={getNameModel(llm_model_id)}
            type="subBody"
            variant="regular"
            className="text-Primary-Color"
            ellipsis
            elementType="div"
          />
        </div>

        {language && (
          <div className="flex items-center justify-center gap-1">
            <Avatar avatarUrl={languageFlag} className="!size-[20px]" />
            <Text
              value={t(language)}
              type="subBody"
              variant="regular"
              className="text-Primary-Color"
            />
          </div>
        )}
      </div>
    </div>
  )
}

export default memo(WorkerItem)
