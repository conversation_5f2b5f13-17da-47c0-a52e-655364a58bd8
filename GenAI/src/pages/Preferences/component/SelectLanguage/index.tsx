import { LanguageTypes, usersSwitchLanguageUserApi } from '@/apis/client'
import Icon from '@/assets/icon/Icon'
import Message from '@/components/Message'
import Text from '@/components/Text'
import { HTTP_STATUS_CODE } from '@/constants'
import { useMyProfile } from '@/hooks/useMyProfile'
import i18n from '@/i18n'
import { colors } from '@/theme'
import { Popover } from 'antd'
import clsx from 'clsx'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'

const SelectLanguage = () => {
  const { myProfile, fetchMyProfile } = useMyProfile()
  const { t } = useTranslation()
  const currentLanguage = i18n.language
  const [isActive, setIsActive] = useState(false)
  const data: {
    label: string
    labelTranslation: string
    value: LanguageTypes
    disable: boolean
  }[] = [
    {
      label: t('preferences.english'),
      labelTranslation: 'English',
      value: 'en',
      disable: false,
    },
    {
      label: t('preferences.vietnamese'),
      labelTranslation: 'Tiếng Việt',
      value: 'vi',
      disable: false,
    },
    {
      label: t('preferences.french'),
      labelTranslation: 'Français',
      value: 'fr',
      disable: true,
    },
    {
      label: t('preferences.german'),
      labelTranslation: 'Deutsch',
      value: 'de',
      disable: true,
    },
    {
      label: t('preferences.spanish'),
      labelTranslation: 'Español',
      value: 'es',
      disable: true,
    },
    {
      label: t('preferences.japanese'),
      labelTranslation: '日本語',
      value: 'ja',
      disable: true,
    },
    {
      label: t('preferences.russian'),
      labelTranslation: 'русский',
      value: 'ru',
      disable: true,
    },
    {
      label: t('preferences.chinese'),
      labelTranslation: '中文(简体)',
      value: 'zh',
      disable: true,
    },
  ]

  const handleChangeLanguage = async (value: LanguageTypes) => {
    const params = {
      language: value,
    }
    const data = await usersSwitchLanguageUserApi({
      body: params,
      path: {
        user_id: myProfile?.id || 0,
      },
    })

    if (data.status === HTTP_STATUS_CODE.SUCCESS) {
      fetchMyProfile()
      i18n.changeLanguage(value)
      setIsActive(false)
      window.location.reload()
    } else {
      Message.error({
        message: t('tool_category.something_went_wrong'),
      })
    }
  }

  return (
    <Popover
      trigger={['click']}
      arrow={false}
      open={isActive}
      onOpenChange={setIsActive}
      placement="bottom"
      styles={{
        body: {
          backgroundColor: 'white',
          borderRadius: '12px',
          boxShadow:
            '0px 10px 15px -3px rgba(0, 0, 0, 0.10), 0px 4px 6px -2px rgba(0, 0, 0, 0.05)',
          padding: '8px',
          minWidth: '132px',
        },
      }}
      content={
        <div className="flex flex-col gap-[4px]">
          {data.map((item) => (
            <div
              onClick={() => {
                if (!item.disable) {
                  handleChangeLanguage(item.value)
                }
              }}
              key={item.value}
              className={clsx(
                'flex w-full cursor-pointer flex-col rounded-[6px] px-[8px] py-[2px] hover:bg-Hover-Color',
                currentLanguage === item.value && 'bg-Background-Color',
                item.disable && 'cursor-not-allowed opacity-40'
              )}
            >
              <Text
                type="subBody"
                variant="regular"
                className={clsx(
                  'text-Primary-Color',
                  currentLanguage === item.value &&
                    'text-linear-gradient-main2-color'
                )}
              >
                {item.label}
              </Text>
              <Text
                type="subBody"
                variant="regular"
                className="text-Tertiary-Color"
              >
                {item.labelTranslation}
              </Text>
            </div>
          ))}
        </div>
      }
    >
      <div className="flex cursor-pointer items-center gap-[4px] rounded-[6px] px-[12px] py-[4px] hover:bg-neutral-200">
        <Text type="body" variant="medium" className="text-Tertiary-Color">
          {data.find((item) => item.value === currentLanguage)?.label}
        </Text>
        <Icon
          name="chevron-up"
          className={clsx('transition-all', isActive ? '' : 'rotate-180')}
          size={16}
          color={colors['neutral'][400]}
        />
      </div>
    </Popover>
  )
}

export default SelectLanguage
