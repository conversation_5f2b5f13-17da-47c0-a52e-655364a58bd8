import Modal from '@/components/Modal'
import Select from '@/components/SearchBar/components/Select'
import { cn } from '@/helpers'
import { useTranslation } from 'react-i18next'
import SelectShareData from '../SearchableInput'
import Icon from '@/assets/icon/Icon'
import { useState } from 'react'

interface IProps {
  open: boolean
  onClose: () => void
}

const ModalDataAccess = (props: IProps) => {
  const { t } = useTranslation()
  const { open, onClose } = props

  // Example usage:
  const shareDataOptions = [
    { id: '1', name: 'Public', icon: <Icon name="AI-Automation" /> },
    { id: '2', name: 'Private', icon: <Icon name="Bold-Arrow-Up" /> },
    { id: '3', name: 'Team Only', icon: <Icon name="Bold-Arrow-Left" /> },
  ]

  const [selectedOption, setSelectedOption] = useState()

  return (
    <Modal
      open={open}
      title={'Grant data access'}
      subTitle={'Select who you want to allow access to this data source'}
      onClickClose={onClose}
      onClickCancel={onClose}
      className="w-[522px] border-none"
      showFooter={false}
    >
      <div className="flex h-[105px] w-full flex-col gap-[8px] rounded-[12px] border border-neutral-200 bg-white px-[24px] pb-[24px] pt-[16px]">
        <div className="px-[4px] text-[14px] font-medium leading-[21px] text-Primary-Color">
          Share to entire group
        </div>

        <SelectShareData />
        {/* <Select
          allowRemove
          placement={'bottom'}
          overlayClassName={cn('[--anchor-gap:8px]')}
          contentClassName={cn('!max-w-full')}
          containerClassName={cn(
            'w-full border border-border-base-icon h-[36px] max-w-full rounded-lg px-[12px] py-[7px]',
            'group-data-[open]:border-Base-Neutral group-data-[open]:shadow-focus'
          )}
          loading={false}
          data={[]}
          selected={{ id: '', name: '' }}
          multipleChoice
          // onChangeSelectedValue={(value) => handleGroupChange(value)}
          // renderSelectedName={({ selected }) => {
          //   const groups = selected as typeof selectedGroups

          //   return groups.map((group) => group.name).join(', ')
          // }}
        /> */}
      </div>
    </Modal>
  )
}

export default ModalDataAccess
