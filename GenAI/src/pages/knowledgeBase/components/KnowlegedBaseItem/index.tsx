import Icon from '@/assets/icon/Icon'
import Dropdown, { SelectedItemProps } from '@/components/Dropdown'
import More from '@/components/More'
import Text from '@/components/Text'
import { formatBytes, getUrlImage } from '@/helpers'
import { isEmpty } from 'lodash'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { KnowledgeBaseActionItems, KnowledgeBaseActions } from '../../types'

const bgColors = [
  'bg-slate-50',
  'bg-gray-50',
  'bg-zinc-50',
  'bg-neutral-50',
  'bg-stone-50',
  'bg-red-50',
  'bg-orange-50',
  'bg-amber-50',
  'bg-yellow-50',
  'bg-lime-50',
  'bg-green-50',
  'bg-emerald-50',
  'bg-teal-50',
  'bg-cyan-50',
  'bg-sky-50',
  'bg-blue-50',
  'bg-indigo-50',
  'bg-violet-50',
  'bg-purple-50',
  'bg-fuchsia-50',
  'bg-pink-50',
  'bg-rose-50',
]

enum LoadImage {
  none = 'none',
  success = 'success',
  error = 'error',
}

const KnowlegedBaseItem = ({
  itemId,
  image,
  number,
  title,
  totalSize,
  onDelete,
  onClick,
  onClickItem,
  openModal,
}: KnowlegedBaseProps) => {
  const { t } = useTranslation()
  const [isHover, setIsHover] = useState(false)
  const [loadImageState, setLoadImageState] = useState<LoadImage>(
    LoadImage.none
  )
  const [openWorkerAction, setOpenWorkerAction] = useState(false)

  const handleImageLoad = () => {
    setLoadImageState(LoadImage.success)
  }

  const handleImageError = () => {
    setLoadImageState(LoadImage.error)
  }

  const iconBg = useMemo(() => {
    if (!image || loadImageState === LoadImage.error) {
      return 'bg-Background-Color'
    }
    const randomIndex = Math.floor(Math.random() * bgColors.length)
    return bgColors[randomIndex]
  }, [image, loadImageState])

  useEffect(() => {
    if (!image) {
      setLoadImageState(LoadImage.error)
    } else {
      setLoadImageState(LoadImage.success)
    }
  }, [image])

  const displayNumber = useMemo(() => {
    if (number < 2) {
      return `${number} ${t('knowledge_base.file')}`
    }
    return `${number} ${t('knowledge_base.files')}`
  }, [number])

  const handleSelectWorkerAction = useCallback(({ key }: SelectedItemProps) => {
    switch (key) {
      case KnowledgeBaseActions.EDIT:
        onClick(itemId)
        break
      case KnowledgeBaseActions.DELETE:
        onDelete(itemId)
        break
      case KnowledgeBaseActions.DATA_ACCESS:
        openModal(itemId)
        break
    }
  }, [])

  return (
    <div
      className="relative flex h-[210px] w-[252px] cursor-pointer flex-col items-center rounded-[20px] bg-white px-[16px] pb-[16px] pt-[12px] shadow hover:bg-Hover-Color"
      onMouseEnter={() => setIsHover(true)}
      onMouseLeave={() => setIsHover(false)}
      onClick={() => {
        onClickItem?.(itemId)
      }}
    >
      <div className="flex w-full flex-col gap-[12px] p-[4px]">
        <div className="flex w-full flex-col gap-[8px]">
          {loadImageState !== LoadImage.error && !isEmpty(image) ? (
            <img
              src={getUrlImage(image)}
              alt="img"
              className="h-[48px] w-[48px] rounded-full"
              onLoad={handleImageLoad}
              onError={handleImageError}
            />
          ) : (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="32"
              height="32"
              viewBox="0 0 32 32"
              fill="none"
            >
              <path
                d="M16.4444 13.6132C11.1769 13.6132 5.33325 11.3498 5.33325 8.13991C5.33325 4.93004 11.1769 2.66666 16.4444 2.66666C21.7119 2.66666 27.5555 4.93004 27.5555 8.18107C27.5555 11.4321 21.7119 13.6543 16.4444 13.6543V13.6132Z"
                fill="url(#paint0_linear_19583_23600)"
              />
              <path
                d="M16.4444 21.4321C11.1769 21.4321 5.33325 19.1687 5.33325 15.9588V12.7901C7.76124 14.8066 11.7942 16.1235 16.4444 16.1235C21.0946 16.1235 25.0863 14.8066 27.5555 12.7901V16C27.5555 19.2099 21.7119 21.4732 16.4444 21.4732V21.4321Z"
                fill="url(#paint1_linear_19583_23600)"
              />
              <path
                d="M16.4444 29.2922C11.1769 29.2922 5.33325 27.0288 5.33325 23.8189V20.6091C7.76124 22.6255 11.7942 23.9424 16.4444 23.9424C21.0946 23.9424 25.0863 22.6255 27.5555 20.6091V23.8189C27.5555 27.07 21.7119 29.3333 16.4444 29.3333V29.2922Z"
                fill="url(#paint2_linear_19583_23600)"
              />
              <defs>
                <linearGradient
                  id="paint0_linear_19583_23600"
                  x1="27.5555"
                  y1="16"
                  x2="8.13325"
                  y2="23.9192"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop stop-color="#642B73" stop-opacity="0.3" />
                  <stop offset="1" stop-color="#C6426E" stop-opacity="0.3" />
                </linearGradient>
                <linearGradient
                  id="paint1_linear_19583_23600"
                  x1="27.5555"
                  y1="16"
                  x2="8.13325"
                  y2="23.9192"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop stop-color="#642B73" stop-opacity="0.3" />
                  <stop offset="1" stop-color="#C6426E" stop-opacity="0.3" />
                </linearGradient>
                <linearGradient
                  id="paint2_linear_19583_23600"
                  x1="27.5555"
                  y1="16"
                  x2="8.13325"
                  y2="23.9192"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop stop-color="#642B73" stop-opacity="0.3" />
                  <stop offset="1" stop-color="#C6426E" stop-opacity="0.3" />
                </linearGradient>
              </defs>
            </svg>
          )}
          <Text
            type="body"
            variant="semibold"
            className="mt-[8px] !w-fit max-w-[173px] text-center text-Primary-Color"
            elementType="div"
            tooltipPosition="left"
            ellipsis
          >
            {title}
          </Text>
        </div>

        <div className="flex h-full w-full flex-col gap-[8px] rounded-[16px] bg-neutral-100 p-[12px]">
          <div className="w-fullitems-center flex justify-between">
            <Text type="supportText" className="text-Secondary-Color">
              Total files
            </Text>
            <Text type="supportText" className="text-Primary-Color">
              {number}
            </Text>
          </div>
          <div className="flex w-full items-center justify-between">
            <Text type="supportText" className="text-Secondary-Color">
              Total size
            </Text>
            <Text type="supportText" className="text-Primary-Color">
              {formatBytes({ bytes: totalSize })}
            </Text>
          </div>
          <div className="flex w-full items-center justify-between">
            <Text type="supportText" className="text-Secondary-Color">
              Data access
            </Text>
            <Text type="supportText" className="text-Primary-Color">
              {number}
            </Text>
          </div>
        </div>

        {(isHover || openWorkerAction) && (
          <div className="absolute right-2 top-3 flex flex-row-reverse gap-1">
            <Dropdown
              // overlayClassName="w-[71px]"
              open={openWorkerAction}
              items={KnowledgeBaseActionItems}
              onSelect={handleSelectWorkerAction}
              onOpenChange={(newOpen) => {
                setOpenWorkerAction(newOpen)
              }}
            >
              <More active={openWorkerAction} />
            </Dropdown>
          </div>
        )}
        {loadImageState === LoadImage.none && (
          <div className="right-0, absolute bottom-0 left-0 top-0 flex h-[120px] w-[205px] flex-col rounded-[16px] bg-white p-[16px] pt-[8px]">
            <div className="flex animate-pulse flex-col items-center">
              <div className="h-[48px] w-[48px] rounded-[24px] bg-slate-200"></div>
              <div className="mt-[8px] h-[20px] w-[163px] rounded bg-slate-200"></div>
              <div className="mt-[4px] h-[15px] w-[80px] rounded bg-slate-200"></div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default KnowlegedBaseItem

export interface KnowlegedBaseProps {
  itemId: string | null
  image: string | null
  title: string | null
  number: number
  totalSize: number
  onDelete: (id: string | null) => void
  onClick: (id: string | null) => void
  onClickItem?: (id: string | null) => void
  openModal: (id: string | null) => void
}
