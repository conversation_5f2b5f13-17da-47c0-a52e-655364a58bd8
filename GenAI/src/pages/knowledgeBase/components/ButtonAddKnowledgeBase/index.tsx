import Icon from '@/assets/icon/Icon'
import Text from '@/components/Text'
import { memo } from 'react'
import { useTranslation } from 'react-i18next'

const ButtonAddKnowledgeBase = ({ onClick }: ButtonAddKnowledgeBaseProps) => {
  const { t } = useTranslation()
  return (
    <button
      onClick={onClick}
      className="flex h-[210px] w-[252px] flex-col items-center justify-center rounded-[20px] border-[1px] border-dashed border-[#C6426E] p-[16px] hover:bg-Hover-Color"
    >
      <div className="h-[40px] w-[40px] rounded-[8px] bg-Hover-Color p-[4px]">
        <Icon
          name="Bold-MessagesConversation-PenNewRound"
          gradient={['#C6426E', '#642B73']}
          size={32}
        />
      </div>
      <Text
        type="body"
        variant="bold"
        className="mt-[8px] w-[173px] max-w-[173px] overflow-hidden truncate text-center text-Primary-Color"
        elementType="div"
      >
        {t('knowledge_base.add_new_directory')}
      </Text>
    </button>
  )
}

interface ButtonAddKnowledgeBaseProps {
  onClick?: () => void
}

export default memo(ButtonAddKnowledgeBase)
