import React, { useEffect, useRef, useState } from 'react'

const SearchableInput = () => {
  const [inputValue, setInputValue] = useState('')
  const [selectedItems, setSelectedItems] = useState([])
  const [showDropdown, setShowDropdown] = useState(false)
  const [highlightedIndex, setHighlightedIndex] = useState(-1)
  const inputRef = useRef(null)

  // Sample data - có thể thay thế bằng data từ API
  const allItems = [
    { id: 1, name: 'Marketing', type: 'department', avatar: '🎯' },
    { id: 2, name: 'Human Resources', type: 'department', avatar: '👥' },
    {
      id: 3,
      name: 'Sample group name start with H',
      type: 'group',
      avatar: '👥',
    },
    {
      id: 4,
      name: 'Hensikin',
      email: '<EMAIL>',
      type: 'person',
      avatar: '👤',
    },
    {
      id: 5,
      name: '<PERSON><PERSON><PERSON>',
      email: 'hoai<PERSON><EMAIL>',
      type: 'person',
      avatar: '👤',
    },
    { id: 6, name: 'Development', type: 'department', avatar: '💻' },
    { id: 7, name: 'Sales', type: 'department', avatar: '💰' },
    { id: 8, name: 'Support', type: 'department', avatar: '🎧' },
  ]

  // Lọc items dựa trên input value
  const filteredItems = allItems.filter((item) => {
    const isAlreadySelected = selectedItems.some(
      (selected) => selected.id === item.id
    )
    if (inputValue.length === 0) {
      // Khi không có input, hiển thị tất cả items chưa được chọn
      return !isAlreadySelected
    }
    const matchesSearch =
      item.name.toLowerCase().includes(inputValue.toLowerCase()) ||
      (item.email &&
        item.email.toLowerCase().includes(inputValue.toLowerCase()))
    return !isAlreadySelected && matchesSearch
  })

  const handleInputChange = (e) => {
    const value = e.target.value
    setInputValue(value)
    setShowDropdown(true) // Luôn hiển thị dropdown khi có thay đổi
    setHighlightedIndex(-1)
  }

  const handleKeyDown = (e) => {
    if (!showDropdown || filteredItems.length === 0) return

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setHighlightedIndex((prev) =>
          prev < filteredItems.length - 1 ? prev + 1 : 0
        )
        break
      case 'ArrowUp':
        e.preventDefault()
        setHighlightedIndex((prev) =>
          prev > 0 ? prev - 1 : filteredItems.length - 1
        )
        break
      case 'Enter':
        e.preventDefault()
        if (highlightedIndex >= 0) {
          addItem(filteredItems[highlightedIndex])
        }
        break
      case 'Escape':
        setShowDropdown(false)
        setHighlightedIndex(-1)
        break
    }
  }

  const addItem = (item) => {
    setSelectedItems((prev) => [...prev, item])
    setInputValue('')
    setShowDropdown(false)
    setHighlightedIndex(-1)
    inputRef.current?.focus()
  }

  const removeItem = (itemId) => {
    setSelectedItems((prev) => prev.filter((item) => item.id !== itemId))
  }

  const handleItemClick = (item) => {
    addItem(item)
  }

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (inputRef.current && !inputRef.current.contains(event.target)) {
        setShowDropdown(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const getItemIcon = (type) => {
    switch (type) {
      case 'department':
        return '🏢'
      case 'group':
        return '👥'
      case 'person':
        return '👤'
      default:
        return '📁'
    }
  }

  return (
    <div className="mx-auto w-full max-w-2xl p-6">
      <div className="relative" ref={inputRef}>
        {/* Textarea với selected items */}
        <div className="h-24 overflow-y-auto rounded-lg border border-gray-300 bg-white p-2 focus-within:border-blue-500 focus-within:ring-2 focus-within:ring-blue-200">
          <div className="flex flex-wrap items-start gap-2">
            {/* Hiển thị các items đã được chọn */}
            {selectedItems.map((item) => (
              <div
                key={item.id}
                className="flex flex-shrink-0 items-center gap-2 rounded-full bg-blue-100 px-3 py-1 text-sm text-blue-800"
              >
                <span>{getItemIcon(item.type)}</span>
                <span>{item.name}</span>
                <button
                  onClick={() => removeItem(item.id)}
                  className="rounded-full p-0.5 hover:bg-blue-200"
                >
                  x
                </button>
              </div>
            ))}

            {/* Textarea field */}
            <textarea
              value={inputValue}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              onFocus={() => setShowDropdown(true)} // Hiển thị dropdown khi focus
              placeholder={
                selectedItems.length === 0 ? 'Tìm kiếm và chọn items...' : ''
              }
              className="min-h-[20px] min-w-[200px] flex-1 resize-none bg-transparent outline-none"
              rows={1}
            />
          </div>
        </div>

        {/* Dropdown list */}
        {showDropdown && filteredItems.length > 0 && (
          <div className="absolute left-0 right-0 top-full z-50 mt-1 max-h-60 overflow-y-auto rounded-lg border border-gray-300 bg-white shadow-lg">
            {filteredItems.map((item, index) => (
              <div
                key={item.id}
                onClick={() => handleItemClick(item)}
                className={`flex cursor-pointer items-center gap-3 p-3 hover:bg-gray-50 ${
                  index === highlightedIndex ? 'bg-blue-50' : ''
                }`}
              >
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-200 text-sm">
                  {getItemIcon(item.type)}
                </div>
                <div className="flex-1">
                  <div className="font-medium text-gray-900">{item.name}</div>
                  {item.email && (
                    <div className="text-sm text-gray-500">{item.email}</div>
                  )}
                </div>
                <div className="text-xs capitalize text-gray-400">
                  {item.type}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Hiển thị kết quả đã chọn */}
      {selectedItems.length > 0 && (
        <div className="mt-4 rounded-lg bg-gray-50 p-4">
          <h3 className="mb-2 font-medium text-gray-900">
            Đã chọn ({selectedItems.length}):
          </h3>
          <div className="space-y-2">
            {selectedItems.map((item) => (
              <div key={item.id} className="flex items-center gap-3 text-sm">
                <span>{getItemIcon(item.type)}</span>
                <span className="font-medium">{item.name}</span>
                {item.email && (
                  <span className="text-gray-500">({item.email})</span>
                )}
                <span className="ml-auto text-xs capitalize text-gray-400">
                  {item.type}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default SearchableInput
