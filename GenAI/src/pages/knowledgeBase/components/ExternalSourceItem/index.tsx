import { KBExternalEmbeddingStatusTypes } from '@/apis/client'
import Icon from '@/assets/icon/Icon'
import IconButton from '@/components/IconButton'
import Text from '@/components/Text'
import { formatBytes, getUrlImage } from '@/helpers'
import { colors } from '@/theme'
import clsx from 'clsx'
import { isEmpty } from 'lodash'
import { useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { LoadImage, bgColors } from '../../constants'

interface ExternalSourceItemProps {
  itemId: string | null
  image?: string | null
  displayName: string | null
  description: string | null
  isSensitive: boolean | null
  totalSize: number
  embeddingStatus: KBExternalEmbeddingStatusTypes | string | null
  onDelete: (id: string | null) => void
  onClickItem?: (id: string | null) => void
}

const ExternalSourceItem = ({
  itemId,
  image,
  totalSize,
  displayName,
  description = 'Sample source description',
  isSensitive = true,
  embeddingStatus = KBExternalEmbeddingStatusTypes.NEW,
  onDelete,
  onClickItem,
}: ExternalSourceItemProps) => {
  const { t } = useTranslation()
  const [isHover, setIsHover] = useState(false)
  const [loadImageState, setLoadImageState] = useState<LoadImage>(
    LoadImage.none
  )
  const handleImageLoad = () => {
    setLoadImageState(LoadImage.success)
  }

  const handleImageError = () => {
    setLoadImageState(LoadImage.error)
  }

  const iconBg = useMemo(() => {
    if (!image || loadImageState === LoadImage.error) {
      return 'bg-Background-Color'
    }
    const randomIndex = Math.floor(Math.random() * bgColors.length)
    return bgColors[randomIndex]
  }, [image, loadImageState])

  useEffect(() => {
    if (!image) {
      setLoadImageState(LoadImage.error)
    } else {
      setLoadImageState(LoadImage.success)
    }
  }, [image])

  const displayBytes = useMemo(() => {
    if (
      embeddingStatus === KBExternalEmbeddingStatusTypes.NEW ||
      embeddingStatus === KBExternalEmbeddingStatusTypes.PROCESSING
    ) {
      return (
        <div className="flex items-center gap-[1px]">
          {t('knowledge_base.syncing_data')}
          <Icon name="Bold-Typing-Ball-Animation" size={9} />
        </div>
      )
    } else if (
      embeddingStatus === KBExternalEmbeddingStatusTypes.QUOTA_EXCEEDED
    ) {
      return t('knowledge_base.limit_exceeded')
    }
    return formatBytes({ bytes: totalSize })
  }, [totalSize, embeddingStatus])

  return (
    <div
      className="relative flex h-[224px] w-[256px] cursor-pointer flex-col items-center rounded-xl bg-white px-[16px] pb-[16px] pt-[12px] shadow duration-300 hover:bg-Hover-Color"
      onMouseEnter={() => setIsHover(true)}
      onMouseLeave={() => setIsHover(false)}
      onClick={() => {
        onClickItem?.(itemId)
      }}
    >
      <div className="flex w-full flex-col gap-[8px] py-[4px]">
        <div className="flex flex-col gap-[8px] px-[4px]">
          <div
            className={`flex h-[48px] min-h-[48px] w-[48px] items-center justify-center rounded-full ${iconBg}`}
          >
            {loadImageState !== LoadImage.error && !isEmpty(image) ? (
              <img
                src={getUrlImage(image)}
                alt="img"
                className="h-[32px] w-[32px] rounded-full"
                onLoad={handleImageLoad}
                onError={handleImageError}
              />
            ) : (
              <Icon
                name="database-01"
                size={32}
                gradient={['#642B734D', '#C6426E4D']}
              />
            )}
          </div>

          <div className="flex w-full flex-col gap-[4px]">
            <Text
              type="body"
              variant="medium"
              className="text-Primary-Color"
              elementType="div"
              ellipsis
            >
              {displayName}
            </Text>
            <Text
              type="subBody"
              className="min-h-[36px] overflow-hidden break-words text-Secondary-Color"
              elementType="div"
              multipleLine={2}
              tooltipPosition="bottom"
              ellipsis
            >
              {description}
            </Text>
          </div>

          <div className="flex h-full w-full flex-col gap-[8px] rounded-[12px] bg-neutral-100 p-[12px]">
            <div className="flex items-center justify-between">
              <Text type="supportText" className="text-Secondary-Color">
                Total size
              </Text>
              <Text type="supportText" className="text-Primary-Color">
                {displayBytes}
              </Text>
            </div>

            <div className="flex items-center justify-between">
              <Text type="supportText" className="text-Secondary-Color">
                Data access
              </Text>
              <Text type="supportText" className="text-Primary-Color">
                2632763 members
              </Text>
            </div>
          </div>
        </div>
      </div>
      {isSensitive && (
        <div className="absolute left-2 top-2">
          <Icon
            name="Bold-Security-ShieldKeyhole"
            size={20}
            gradient={['#642B734D', '#C6426E4D']}
          />
        </div>
      )}

      <IconButton
        className={clsx(
          'absolute right-[8px] top-[8px] cursor-pointer duration-300',
          {
            'invisible opacity-0': !isHover,
          }
        )}
        nameIcon="Customize-Delete"
        sizeIcon={24}
        colorIcon={colors['border-base-icon']}
        hoverColor={['#642B73', '#C6426E']}
        onClick={() => onDelete?.(itemId)}
      />

      {loadImageState === LoadImage.none && (
        <div className="right-0, absolute bottom-0 left-0 top-0 flex h-[120px] w-[205px] flex-col rounded-[16px] bg-white p-[16px] pt-[8px]">
          <div className="flex animate-pulse flex-col items-center">
            <div className="h-[48px] w-[48px] rounded-[24px] bg-slate-200"></div>
            <div className="mt-[8px] h-[20px] w-[163px] rounded bg-slate-200"></div>
            <div className="mt-[4px] h-[15px] w-[80px] rounded bg-slate-200"></div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ExternalSourceItem
