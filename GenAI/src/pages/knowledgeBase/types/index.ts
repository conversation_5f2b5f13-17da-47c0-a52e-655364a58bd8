import i18n from '@/i18n'

export enum KnowledgeBaseActions {
  EDIT = 'edit',
  DELETE = 'delete',
  DATA_ACCESS = 'data_access',
}

export const KnowledgeBaseActionItems = [
  {
    key: KnowledgeBaseActions.EDIT,
    label: i18n.t('knowledge_base.edit'),
  },
  {
    key: KnowledgeBaseActions.DELETE,
    label: i18n.t('knowledge_base.delete'),
  },
  {
    key: KnowledgeBaseActions.DATA_ACCESS,
    label: i18n.t('knowledge_base.data_access'),
  },
]
