import {
  KBExternalEmbeddingStatusTypes,
  KBExternalListPublic,
  KBExternalPublic,
  KnowledgeBaseDirectoryPublic,
  externalKnowledgeBaseDeleteExternalKnowledgeBaseApi,
  externalKnowledgeBaseFetchMyExternalKnowledgeBaseApi,
  knowledgeBaseDirectoryDeleteKnowledgeBaseDirectoryApi,
  knowledgeBaseDirectoryReadKnowledgeDirectoriesApi,
} from '@/apis/client'
import Icon from '@/assets/icon/Icon'
import Button from '@/components/Button'
import { MessageDialog } from '@/components/DialogMessage'
import EmptyData from '@/components/EmptyData'
import Layout from '@/components/Layout'
import Message from '@/components/Message'
import NoDataFound from '@/components/NoDataFound'
import PageHeader from '@/components/PageHeader'
import Pagination from '@/components/Pagination'
import SearchBar from '@/components/SearchBar'
import Segmented from '@/components/Segmented'
import { HTTP_STATUS_CODE, PAGE_SIZE } from '@/constants'
import useWSExternalDataSource from '@/hooks/useWSExternalDataSource'
import i18n from '@/i18n'
import { rootUrls } from '@/routes/rootUrls'
import useExternalWsStore from '@/store/externalWSStore'
import clsx from 'clsx'
import { isEmpty } from 'lodash'
import { useCallback, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { generatePath, useLocation, useNavigate } from 'react-router-dom'
import ButtonAddExternalSource from './components/ButtonAddExternalSource'
import ButtonAddKnowledgeBase from './components/ButtonAddKnowledgeBase'
import ExternalSourceItem from './components/ExternalSourceItem'
import KnowlegedBaseItem from './components/KnowlegedBaseItem'
import ListExternalKBSkeleton from './components/ListExternalKBSkeleton'
import ListSkeleton from './components/ListSkeleton'
import ModalAddNewKnowlegedBase from './components/ModalAddNewKnowledgeBase'
import ModalUpdateKnowlegedBase from './components/ModalUpdateKnowledgeBase'
import { EXTERNAL_KB_EMBEDDING_STATUS } from './constants'
import ModalDataAccess from './components/ModalDataAccess'

enum TABS {
  INTERNAL = 'internal',
  EXTERNAL = 'external',
}

const knowledgeBaseButtonAddNew = 'knowledgeBaseButtonAddNew'
const CREATE_ITEM = {
  id: knowledgeBaseButtonAddNew,
  name: '',
  logo: '',
}
const ERR_QUOTA_EXCEEDED = i18n.t(
  'knowledge_base.not_enough_storage_available_to_complete'
)

interface IPagination {
  query: string
  page: number
  totalPage: number
  activeTab?: TABS | string | null
}

interface IPaginationItem {
  key: 'query' | 'page' | 'totalPage' | 'activeTab'
  value?: any
}

const KnowlegedBase = () => {
  const { t } = useTranslation()
  const { state, search } = useLocation()
  const navigate = useNavigate()

  useWSExternalDataSource()

  const [initializing, setInitializing] = useState(true)
  const [isFirstLoad, setIsFirstLoad] = useState(true)
  const [loading, setLoading] = useState(true)
  const [pagination, setPagination] = useState<IPagination>({
    query: '',
    page: 1,
    totalPage: 1,
    activeTab: new URLSearchParams(search).get('type') ?? TABS.INTERNAL,
  })
  const [data, setData] = useState<
    KnowledgeBaseDirectoryPublic[] | KBExternalListPublic
  >([])
  const { query, page, totalPage, activeTab } = pagination

  const [openCreateModal, setOpenCreateModal] = useState(
    state?.isCreate ?? false
  )

  const [openEditModal, setOpenEditModal] = useState(false)

  const [idDetailTool, setIdDetailTool] = useState<string | null>('')

  const [isWaitingKnExternalLoadDone, setIsWaitingKnExternalLoadDone] =
    useState(true)

  const [openModalDataAccess, setOpenModalDataAccess] = useState(false)

  const handleChangePagination = useCallback(
    ({ key, value }: IPaginationItem) =>
      setPagination((props) => ({
        ...props,
        [key]: value,
      })),
    []
  )

  const handleChangeSearch = useCallback((value: string) => {
    handleChangePagination({
      key: 'query',
      value,
    })
  }, [])

  const handleChangeActiveTab = useCallback((value: string) => {
    setPagination((props) => ({
      ...props,
      query: '',
      activeTab: value,
    }))
  }, [])

  const fetchDataByTab = useCallback(
    async ({ tQuery = '', tPage = 1 }: IFetchData) => {
      if (activeTab === TABS.INTERNAL) {
        return await knowledgeBaseDirectoryReadKnowledgeDirectoriesApi({
          query: {
            page_number: tPage,
            page_size: PAGE_SIZE.LARGE,
            name: tQuery || undefined,
          },
        })
      }

      return await externalKnowledgeBaseFetchMyExternalKnowledgeBaseApi({
        query: {
          page_number: tPage,
          page_size: PAGE_SIZE.LARGE,
          name: tQuery || undefined,
        },
      })
    },
    [activeTab]
  )

  const fetchData = async ({ tQuery = '', tPage = 1 }: IFetchData) => {
    try {
      setLoading(true)
      const res = await fetchDataByTab({
        tQuery,
        tPage,
      })

      if (res.status === 200) {
        setIsWaitingKnExternalLoadDone(false)

        const arr: KnowledgeBaseDirectoryPublic[] | KBExternalListPublic =
          res?.data?.data?.data || []
        if (tQuery.length > 0) {
          setData(arr)
        } else {
          if (arr.length > 0) {
            setData([CREATE_ITEM, ...(arr as KnowledgeBaseDirectoryPublic[])])
          } else {
            setData(arr)
          }
        }
        setPagination({
          ...pagination,
          query: tQuery,
          page: tPage,
          totalPage: res?.data?.data?.total_pages || 1,
        })
      } else {
        setData([])
      }
    } catch (error) {
      Message.error({ message: t('common.something_went_wrong') })
      setData([CREATE_ITEM])
    } finally {
      setLoading(false)
      if (isFirstLoad) {
        setIsFirstLoad(false)
      }
    }
  }

  const refreshData = async () => {
    if (page > totalPage) {
      setData([CREATE_ITEM])
      return
    }
    try {
      const res = await fetchDataByTab({
        tQuery: query,
        tPage: page,
      })

      if (res.status === 200) {
        const arr: KnowledgeBaseDirectoryPublic[] | KBExternalListPublic =
          res?.data?.data?.data || []
        if (query.length > 0) {
          setData(arr)
        } else {
          if (arr.length > 0) {
            setData([CREATE_ITEM, ...(arr as KnowledgeBaseDirectoryPublic[])])
          } else {
            if (page > 1) {
              refresh(1)

              fetchData({
                tQuery: query,
                tPage: 1,
                tTotalPage: totalPage,
              })
            } else {
              setData(arr)
            }
          }
        }
        setPagination({
          ...pagination,
          totalPage: res?.data?.data?.total_pages || 1,
        })
      }
    } catch (error) {
      setData([CREATE_ITEM])
      Message.error({ message: t('common.something_went_wrong') })
    }
  }

  const refresh = (pageCurrent: number | null) => {
    if (pageCurrent) {
      if (pageCurrent === page) {
        refreshData()
      } else {
        setPagination({
          ...pagination,
          page: pageCurrent,
        })
      }
    } else {
      refreshData()
    }
  }

  const newKnowlegedBase = () => {
    if (activeTab === TABS.INTERNAL) {
      setOpenCreateModal(true)
      return
    }

    navigate(`${generatePath(rootUrls.ExternalKnowledgeBase)}`)
  }

  const editKnowlegedBase = (id: string | null) => {
    setOpenEditModal(true)
    setIdDetailTool(id)
  }

  const handleDeleteToolCategory = async (id: string | null) => {
    try {
      if (id) {
        const data =
          await knowledgeBaseDirectoryDeleteKnowledgeBaseDirectoryApi({
            path: {
              knowledge_base_directory_id: id,
            },
          })
        if (data.status === HTTP_STATUS_CODE.NO_CONTENT) {
          refresh(null)
          Message.success({
            message: t(
              'knowledge_base.successfully_deleted_knowledge_directory'
            ),
          })
        } else {
          Message.error({ message: t('common.something_went_wrong') })
        }
      }
    } catch (error) {
      Message.error({ message: t('common.something_went_wrong') })
    }
  }

  const deleteToolCategory = (id: string | null) => {
    MessageDialog.warning({
      mainMessage: t('knowledge_base.delete_directory'),
      subMessage: t('knowledge_base.delete_directory_sub_message'),
      onClick: () => handleDeleteToolCategory(id),
    })
  }

  const handleDeleteExternalKB = useCallback(
    async (id: string | null) => {
      try {
        if (id) {
          const data =
            await externalKnowledgeBaseDeleteExternalKnowledgeBaseApi({
              path: {
                external_knowledge_base_id: id,
              },
            })
          if (data.status === HTTP_STATUS_CODE.NO_CONTENT) {
            refresh(null)
            Message.success({
              message: t('knowledge_base.successfully_deleted_data_source'),
            })
          } else {
            Message.error({ message: t('common.something_went_wrong') })
          }
        }
      } catch (error) {
        Message.error({ message: t('common.something_went_wrong') })
      }
    },
    [refresh]
  )

  const deleteExternalKB = useCallback(
    (id: string | null) => {
      MessageDialog.warning({
        mainMessage: t('knowledge_base.delete_external_data_source'),
        subMessage: t('knowledge_base.delete_external_data_source_sub_message'),
        onClick: () => handleDeleteExternalKB(id),
      })
    },
    [handleDeleteExternalKB]
  )

  useEffect(() => {
    if (!isFirstLoad) {
      fetchData({
        tQuery: query,
        tPage: 1,
      })
    }
  }, [query, activeTab])

  useEffect(() => {
    setInitializing(true)
    setTimeout(() => {
      setInitializing(false)
    }, 750)
    fetchData({})
  }, [])

  const { messageQueue, clearMessageQueue } = useExternalWsStore((state) => ({
    messageQueue: state.messageQueue,
    clearMessageQueue: state.clearMessageQueue,
  }))

  useEffect(() => {
    if (
      !isWaitingKnExternalLoadDone &&
      activeTab === TABS.EXTERNAL &&
      messageQueue.length > 0
    ) {
      messageQueue.forEach((m) => {
        // check if message time is older than 2s
        if (Date.now() - m.timestamp > 2000) {
          return
        }

        const message = m.message

        if (message.success) {
          Message.success({
            message: message.message ?? '',
          })

          //update local data
          if (message.data) {
            const newData = data.map((item) => {
              if (item.id === message.data?.external_knowledge_base_id) {
                return {
                  ...item,
                  embedding_status: EXTERNAL_KB_EMBEDDING_STATUS.COMPLETED,
                  total_size: message.data?.total_size ?? 0,
                }
              }
              return item
            })
            setData(newData as KBExternalListPublic)
          }
        } else {
          Message.error({
            message: message.message ?? '',
          })

          //update local data
          if (message.data) {
            const newData = data.map((item) => {
              if (item.id === message.data?.external_knowledge_base_id) {
                return {
                  ...item,
                  embedding_status:
                    (message.message === ERR_QUOTA_EXCEEDED &&
                      (message.data?.total_size ?? 0)) === 0
                      ? KBExternalEmbeddingStatusTypes.QUOTA_EXCEEDED
                      : KBExternalEmbeddingStatusTypes.COMPLETED,
                  total_size: message.data?.total_size ?? 0,
                }
              }
              return item
            })
            setData(newData as KBExternalListPublic)
          }
        }
      })

      clearMessageQueue()
    }
  }, [messageQueue, activeTab, isWaitingKnExternalLoadDone])

  const handleClickItem = (id: string | null) => {
    if (!id) return

    const targetUrl =
      activeTab === TABS.INTERNAL
        ? rootUrls.KnowledgeBaseDirectory
        : rootUrls.ExternalKnowledgeBaseDetail

    navigate(generatePath(targetUrl, { id }))
  }

  useEffect(() => {
    if (search) {
      const searchParams = new URLSearchParams(search)
      const type = searchParams.get('type')

      if (type === TABS.INTERNAL) {
        handleChangeActiveTab(TABS.INTERNAL)
      } else if (type === TABS.EXTERNAL) {
        handleChangeActiveTab(TABS.EXTERNAL)
      }
    }
  }, [search])

  return (
    <Layout>
      <div className="flex h-full flex-col gap-4 overflow-hidden">
        <div className="flex flex-col gap-3">
          <div className="flex items-center justify-between gap-1">
            <PageHeader
              breadcrumbPaths={[
                {
                  name: t('knowledge_base.knowledge_base'),
                },
              ]}
              title={t('knowledge_base.knowledge_base_description')}
            />
            <SearchBar onSearch={handleChangeSearch} />
          </div>
          <div className="flex w-full justify-end">
            <Segmented
              options={[
                {
                  label: t('knowledge_base.internal'),
                  value: TABS.INTERNAL,
                },
                {
                  label: t('knowledge_base.external'),
                  value: TABS.EXTERNAL,
                },
              ]}
              value={activeTab}
              onChange={(value) => {
                navigate(`${rootUrls.KnowledgeBase}?type=${value}`, {
                  replace: true,
                })
              }}
            />
          </div>
        </div>

        <div className="flex h-full flex-col overflow-hidden">
          <div className="genai-scrollbar h-full overflow-y-auto overflow-x-hidden">
            {(initializing || loading) &&
              (activeTab === TABS.INTERNAL ? (
                <ListSkeleton number={PAGE_SIZE.MEDIUM} />
              ) : (
                <ListExternalKBSkeleton number={PAGE_SIZE.MEDIUM} />
              ))}
            {!initializing &&
              !loading &&
              data.length === 0 &&
              query.length === 0 && (
                <div className="flex flex-col items-center">
                  <EmptyData
                    size="medium"
                    type="03"
                    className="mb-[24px] mt-[100px]"
                  />
                  {activeTab === TABS.INTERNAL ? (
                    <Button
                      type="primary"
                      size="medium"
                      onClick={newKnowlegedBase}
                      className="w-[168px]"
                      text={t('knowledge_base.new_directory')}
                      leftIcon={
                        <Icon
                          name="vuesax-outline-add"
                          color="#ffffff"
                          size={20}
                        />
                      }
                    />
                  ) : (
                    <Button
                      type="primary"
                      size="medium"
                      onClick={newKnowlegedBase}
                      className="w-[188px] pr-6"
                      text={t('knowledge_base.new_data_source')}
                      leftIcon={
                        <Icon
                          name="vuesax-outline-add"
                          color="#ffffff"
                          size={20}
                        />
                      }
                    />
                  )}
                </div>
              )}
            {!initializing &&
              !loading &&
              data.length === 0 &&
              query.length !== 0 && <NoDataFound className="mt-[64px]" />}
            {!initializing && !loading && !isEmpty(data) && (
              <div
                className={clsx(
                  'flex flex-wrap gap-x-[24px] gap-y-[20px] pb-[1px]',
                  { '!gap-x-8 !gap-y-6': activeTab === TABS.EXTERNAL }
                )}
              >
                {data.map((item, index) => {
                  if (activeTab === TABS.INTERNAL) {
                    if (item.id === knowledgeBaseButtonAddNew) {
                      return (
                        <ButtonAddKnowledgeBase
                          key={index}
                          onClick={newKnowlegedBase}
                        />
                      )
                    }

                    return (
                      <KnowlegedBaseItem
                        key={`${index}-${item.id}`}
                        itemId={item.id}
                        image={(item as KnowledgeBaseDirectoryPublic)?.logo}
                        title={item.name}
                        number={
                          (item as KnowledgeBaseDirectoryPublic).file_count || 0
                        }
                        totalSize={
                          (item as KnowledgeBaseDirectoryPublic)
                            ?.file_total_size || 0
                        }
                        onDelete={(id) => {
                          deleteToolCategory(id)
                        }}
                        onClick={(id) => {
                          editKnowlegedBase(id)
                        }}
                        onClickItem={handleClickItem}
                        openModal={(id) => {
                          setOpenModalDataAccess(true)
                        }}
                      />
                    )
                  }

                  if (item.id === knowledgeBaseButtonAddNew) {
                    return (
                      <ButtonAddExternalSource
                        key={index}
                        onClick={newKnowlegedBase}
                      />
                    )
                  }

                  return (
                    <ExternalSourceItem
                      key={`${index}-${item.id}`}
                      itemId={item.id}
                      displayName={item.name}
                      description={(item as KBExternalPublic)?.description}
                      totalSize={(item as KBExternalPublic)?.total_size || 0}
                      onDelete={(id) => {
                        deleteExternalKB(id)
                      }}
                      isSensitive={(item as KBExternalPublic)?.is_sensitive}
                      embeddingStatus={
                        (item as KBExternalPublic)?.embedding_status
                      }
                      onClickItem={handleClickItem}
                    />
                  )
                })}
              </div>
            )}
          </div>

          {totalPage > 1 && (
            <div className="flex w-full flex-row justify-end">
              <Pagination
                onChangePage={(e) => {
                  fetchData({
                    tQuery: query,
                    tPage: e,
                  })
                }}
                page={page}
                totalPage={pagination.totalPage ?? 0}
                className="w-[220px]"
              />
            </div>
          )}

          <ModalAddNewKnowlegedBase
            openCreateModal={openCreateModal}
            setOpenCreateModal={setOpenCreateModal}
            refresh={refresh}
          />

          <ModalUpdateKnowlegedBase
            idDetailTool={idDetailTool}
            openEditModal={openEditModal}
            setOpenEditModal={(e) => {
              setOpenEditModal(e)
              setIdDetailTool('')
            }}
            refresh={refresh}
          />

          <ModalDataAccess
            open={openModalDataAccess}
            onClose={() => {
              setOpenModalDataAccess(false)
            }}
          />
        </div>
      </div>
    </Layout>
  )
}

export default KnowlegedBase

interface IFetchData {
  tQuery?: string
  tPage?: number
  tTotalPage?: number
}
