import {
  OrganizationMembersPublic,
  RolePublic,
  organizationsCreateOrganizationMembersApi,
  organizationsVerifyOrganizationMemberEmailsApi,
  rolesReadRolesApi,
} from '@/apis/client'
import Message from '@/components/Message'
import { ISelectBaseItem } from '@/components/Select'
import { HTTP_STATUS_CODE } from '@/constants'
import { useOrg } from '@/hooks/useOrg'
import i18n from '@/i18n'
import { isEmpty } from 'lodash'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'

export enum EmailErrorType {
  FORMAT = 'format',
  EXISTS = 'exists',
}

export enum RoleScope {
  'organization' = 'organization',
  'system' = 'system',
}

export enum Role {
  'Member' = 'Member',
  'Owner' = 'Owner',
}
export interface EmailTag {
  id: string
  email: string
  errorType?: EmailErrorType | null
}

const DEFAULT_GROUP = {
  id: '-1',
  name: i18n.t('group.general'),
}

export const useAddMember = () => {
  const { t } = useTranslation()
  const { listOrgs } = useOrg()

  const orgId = useMemo(() => {
    return listOrgs?.[0]?.id || ''
  }, [listOrgs])

  const [isLoading, setIsLoading] = useState(false)
  const [emails, setEmails] = useState<EmailTag[]>([])

  const [selectedGroups, setSelectedGroups] = useState<ISelectBaseItem[]>([
    DEFAULT_GROUP,
  ])

  const [listRoles, setListRoles] = useState<ISelectBaseItem[]>([])
  const [selectedRole, setSelectedRole] = useState<ISelectBaseItem>()

  const [emailError, setEmailError] = useState('')

  const [isDirtyModal, setDirtyModal] = useState(false)

  const fetchListRoles = useCallback(async () => {
    try {
      const res = await rolesReadRolesApi({
        query: {
          scope: RoleScope.organization,
        },
      })

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        const roles = res.data?.data?.data.map((role: RolePublic) => ({
          id: `${role.id}`,
          name: role.name,
        }))

        // ignore Owner role
        setListRoles(
          roles
            ?.filter((role) => role.name !== Role.Owner)
            ?.map((role) => ({
              ...role,
              name: t(role.name),
            })) || []
        )
        setSelectedRole(roles?.find((role) => role.name === Role.Member))
      }
    } catch (error) {
      Message.error({
        message: t('common.something_went_wrong'),
      })
    }
  }, [t])

  useEffect(() => {
    fetchListRoles()
  }, [])

  const validateEmails = async () => {
    try {
      // Check emails is exists
      const res = await organizationsVerifyOrganizationMemberEmailsApi({
        path: {
          organization_id: Number(orgId),
        },
        query: {
          emails: emails.map((emailTag) => emailTag.email.trim()),
        },
        paramsSerializer: {
          indexes: null, // Prevents array index in query string
        },
      })

      if (res.status !== HTTP_STATUS_CODE.SUCCESS) {
        Message.error({
          message: t('common.something_went_wrong'),
        })
        return false
      }

      const invalidEmails = [
        ...(res?.data?.data?.inexistent_emails || []),
        ...(res?.data?.data?.added_organization_emails || []),
      ]

      const updatedEmails = emails.map((emailTag) => {
        if (invalidEmails.includes(emailTag.email.trim())) {
          return {
            ...emailTag,
            errorType: EmailErrorType.EXISTS,
          }
        }
        return emailTag
      })

      setEmails(updatedEmails)
      return !invalidEmails?.length
    } catch (error) {
      return false
    }
  }

  const resetForm = () => {
    setEmails([])
    setSelectedRole(listRoles?.find((role) => role.name === Role.Member))
    setSelectedGroups([DEFAULT_GROUP])
    setEmailError('')
  }

  const addMembers = async (
    onSuccess?: (newMember: OrganizationMembersPublic) => void,
    onError?: () => void
  ) => {
    try {
      setIsLoading(true)

      if (
        !(await validateEmails()) ||
        !selectedRole ||
        !selectedGroups?.length
      ) {
        return
      }

      const res = await organizationsCreateOrganizationMembersApi({
        path: {
          organization_id: Number(orgId),
        },
        body: {
          emails: emails.map((emailTag) => emailTag.email.trim()),
          role_id: Number(selectedRole.id),
          organization_group_ids: selectedGroups
            ?.filter((group) => group.id !== DEFAULT_GROUP.id) // Filter out the default group
            ?.map((group) => Number(group.id)),
        },
      })

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        const newMembers = res.data?.data?.data || []

        if (newMembers.length > 0) {
          Message.success({
            message: t('org_user_management.add_members_success'),
          })

          resetForm()
          onSuccess?.(newMembers)
        } else {
          Message.error({
            message: t('common.something_went_wrong'),
          })
          onError?.()
        }
      }
    } catch (error: any) {
      Message.error({
        message: t('common.something_went_wrong'),
      })
      onError?.()
    } finally {
      setIsLoading(false)
    }
  }

  const handleRoleChange = (role: ISelectBaseItem) => {
    if (role) {
      setSelectedRole(role)

      setDirtyModal(true)
    }
  }

  const handleGroupChange = (group: ISelectBaseItem) => {
    const isSelected = selectedGroups.find((item) => item.id === group.id)

    if (isSelected) {
      setSelectedGroups((prev) => {
        if (prev.filter((item) => item.id !== group.id)?.length) {
          return prev.filter((item) => item.id !== group.id)
        } else {
          return [DEFAULT_GROUP]
        }
      })
    } else {
      setSelectedGroups((prev) => [
        ...prev.filter((item) => item.id !== DEFAULT_GROUP.id),
        group,
      ])
    }
    setDirtyModal(true)
  }

  const handleEmailsChange = (
    newEmails: EmailTag[] | ((prev: EmailTag[]) => EmailTag[])
  ) => {
    if (typeof newEmails === 'function') {
      setEmails(newEmails)
    } else {
      setEmails(newEmails)
    }

    setDirtyModal(true)
  }

  const handleEmailError = (error: string) => {
    setEmailError(error)
  }

  const isFormValid =
    emails.length > 0 &&
    !isEmpty(selectedRole) &&
    emails.every((emailTag) => emailTag.errorType === null) &&
    emailError === ''

  return {
    // Form state
    emails,
    listRoles,
    selectedRole,
    selectedGroups,
    emailError,
    isLoading,
    isFormValid,
    isDirtyModal,

    // Actions
    handleEmailsChange,
    handleEmailError,
    handleRoleChange,
    handleGroupChange,
    addMembers,
    resetForm,
  }
}
