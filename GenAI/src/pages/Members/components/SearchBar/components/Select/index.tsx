import Icon from '@/assets/icon/Icon'
import IconButton from '@/components/IconButton'
import Popover from '@/components/Popover'
import Text from '@/components/Text'
import { PAGE_SIZE } from '@/constants'
import { cn } from '@/helpers'
import WorkflowItemSkeleton from '@/pages/Dashboard/components/SessionLog/components/SessionList/WorkflowDropdown/components/WorkflowItemSkeleton'
import { colors } from '@/theme'
import { size } from 'lodash'
import { useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import './style.scss'

export interface ISelectBaseItem {
  id: string
  value?: any
  name?: string
  icon?: any
}

interface SelectProps<T extends ISelectBaseItem> {
  data: T[]
  selected?: T | T[]
  placeholder?: string
  getPopupContainer?: (
    triggerNode?: HTMLElement
  ) => Element | DocumentFragment | HTMLElement | ParentNode | null | undefined
  onChangeSelectedValue: (value: any) => void
  className?: string
  overlayClassName?: string
  error?: boolean
  disabled?: boolean
  allowRemove?: boolean
  allowClear?: boolean
  multipleChoice?: boolean
  icon?: React.ReactNode
  containerClassName?: string
  onClear?: () => void
  onOpen?: () => void
  loading?: boolean
  optionMaxWidth?: number
  contentClassName?: string
  listClassName?: string
  itemClassName?: string
  placement?:
    | 'top'
    | 'top start'
    | 'top end'
    | 'bottom'
    | 'bottom start'
    | 'bottom end'
    | 'left'
    | 'left start'
    | 'left end'
    | 'right'
    | 'right start'
    | 'right end'
  renderSelectedName?: (params: {
    selected?: T | T[]
    multipleChoice: boolean
  }) => string
}

const Select = <T extends ISelectBaseItem>({
  data,
  selected,
  placeholder,
  getPopupContainer,
  onChangeSelectedValue,
  className,
  overlayClassName,
  error,
  disabled,
  allowRemove,
  allowClear,
  multipleChoice,
  icon,
  containerClassName,
  onClear,
  onOpen,
  loading,
  placement,
  contentClassName,
  listClassName,
  itemClassName,
  renderSelectedName,
}: SelectProps<T>) => {
  const { t } = useTranslation()
  const [open, setOpen] = useState(false)

  // Helper variables for backward compatibility and cleaner logic
  const selectedSingle = multipleChoice
    ? undefined
    : (selected as T | undefined)
  const selectedMultiple = multipleChoice
    ? (selected as T[] | undefined)
    : undefined

  const handleChangeSelected = (value?: T) => {
    if (!multipleChoice) {
      if (!allowRemove) {
        onChangeSelectedValue(value)
      } else {
        if (selectedSingle?.id === value?.id) {
          onChangeSelectedValue(undefined)
        } else {
          onChangeSelectedValue(value)
        }
      }
    } else {
      onChangeSelectedValue(value)
    }
  }

  const isSelected = (value: T) => {
    if (multipleChoice) {
      return selectedMultiple?.some((item) => item.id === value.id)
    }
    return selectedSingle?.id === value.id
  }

  const selectedName = useMemo(() => {
    // Default logic for displaying selected items
    const getDefaultSelectedName = () => {
      if (multipleChoice) {
        const length = selectedMultiple?.length
        if (!length) {
          return t('common.empty')
        }
        if (length && length > 1) {
          return `${selectedMultiple?.length} ${t('common.selected').toLowerCase()}`
        }
        return selectedMultiple[0]?.name
      }
      return selectedSingle?.name
    }

    // Use custom render function if provided
    if (renderSelectedName) {
      return renderSelectedName({
        selected,
        multipleChoice: !!multipleChoice,
      })
    }

    return getDefaultSelectedName()
  }, [selected, multipleChoice, t, renderSelectedName])

  useEffect(() => {
    if (open && onOpen) {
      onOpen()
    }
  }, [open])

  const clearHandler = () => {
    onClear?.()
    setOpen(false)
  }

  return (
    <div className={className}>
      <Popover
        isPure
        overlayClassName={cn('z-50 !overflow-hidden !p-0', overlayClassName)}
        open={open}
        title={t('common.description')}
        getPopupContainer={getPopupContainer}
        onOpenChange={(value) => {
          if (!disabled) {
            setOpen(value)
          }
        }}
        allowCloseWhenClickButton={allowRemove}
        placement={placement}
        content={
          <div
            className={cn(
              'searchbox-filter-new max-h-[176px] w-[var(--button-width)] max-w-[240px] rounded-xl border border-border-base-icon px-2 py-2 shadow-base',
              'overflow-hidden',
              'flex flex-col',
              contentClassName
            )}
          >
            {loading && (
              <div
                className={cn('flex w-full flex-col gap-1', contentClassName)}
              >
                {Array.from({ length: PAGE_SIZE.EXTRA_SMALL }).map(
                  (_, index) => (
                    <WorkflowItemSkeleton key={index} />
                  )
                )}
              </div>
            )}
            {!loading && !size(data) && (
              <div className="flex h-full w-full flex-col items-center justify-center gap-1 py-1 pr-1">
                <Text
                  type="subBody"
                  variant="regular"
                  className="text-Primary-Color"
                  elementType="div"
                >
                  {t('empty_data.no_available_items2')}
                </Text>
              </div>
            )}
            {!loading && size(data) > 0 && (
              <div className="genai-scrollbar flex size-full flex-col items-center overflow-y-auto overflow-x-hidden">
                <div
                  className={cn('flex w-full flex-col gap-1', listClassName)}
                >
                  {data.map((item) => (
                    <div
                      key={item.id}
                      className={cn(
                        'group flex cursor-pointer select-none items-center rounded-[6px] px-2 py-[6px]',
                        'hover:bg-Hover-Color data-[focus]:bg-Background-Color',
                        {
                          'bg-Hover-Color': isSelected(item),
                        },
                        'max-w-full',
                        itemClassName
                      )}
                      onClick={() => {
                        handleChangeSelected(item)
                        if (!multipleChoice) {
                          setOpen(false)
                        }
                      }}
                    >
                      {item?.icon}
                      <Text
                        className={cn(
                          'overflow-hidden text-Primary-Color',
                          {
                            'bg-Main-Color bg-clip-text text-transparent':
                              isSelected(item),
                          },
                          {
                            'group-hover:!text-transparent': isSelected(item),
                          }
                        )}
                        type="subBody"
                        variant="medium"
                        elementType="div"
                        ellipsis
                      >
                        {item.name}
                      </Text>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        }
      >
        <div className="relative flex w-full cursor-pointer items-center">
          <div
            className={cn(
              'flex h-5 max-w-[190px] items-center gap-1 px-1',
              'hover:border-Main-03',
              'transition duration-75 ease-in',
              { 'border-Error-Color': error },
              containerClassName
            )}
          >
            {icon}
            {(multipleChoice ? selectedMultiple?.length : selectedSingle) ? (
              <Text
                className={cn(
                  'w-full overflow-hidden text-start !text-Primary-Color',
                  disabled && '!text-Disable-Text'
                )}
                type="subBody"
                variant="regular"
                elementType="div"
                ellipsis
              >
                {selectedName ?? ''}
              </Text>
            ) : (
              <Text
                className="w-full overflow-hidden text-start text-Tertiary-Color"
                type="subBody"
                variant="regular"
                elementType="div"
                ellipsis
              >
                {placeholder ?? ''}
              </Text>
            )}
            {allowClear && (
              <IconButton
                nameIcon="vuesax-bold-close-circle"
                sizeIcon={14}
                colorIcon={colors['border-base-icon']}
                hoverColor={colors['Tertiary-Color']}
                className="ml-1"
                onClick={clearHandler}
              />
            )}

            <div
              className={cn('group flex', {
                'genai_button_icon__active rotate-180 transform': open,
              })}
            >
              <Icon
                name="Outline-Chevron-Down"
                size={14}
                color={colors['Tertiary-Color']}
              />
            </div>
          </div>
        </div>
      </Popover>
    </div>
  )
}

export default Select
