import { memo } from 'react'
import { useTranslation } from 'react-i18next'
import SearchBar from '../SearchBar'
import { ISelectBaseItem } from '@/components/Select'
import Button from '@/components/Button'
import IconTrash01 from './IconTrash01'
import Text from '@/components/Text'

interface MembersFiltersProps {
  onSearch: (value: string) => void
  selectedRoleFilter: ISelectBaseItem | undefined
  selectedGroupFilters: ISelectBaseItem[]
  availableGroups: ISelectBaseItem[]
  availableRoles: ISelectBaseItem[]
  onRoleFilterChange: (value: ISelectBaseItem | null) => void
  onGroupFilterChange: (value: ISelectBaseItem) => void
  onGroupFilterClear: () => void
  onAddMember: () => void
  selectedCount?: number
  onDelete?: () => void
}

const MembersFilters = memo(
  ({
    onSearch,
    selectedRoleFilter,
    selectedGroupFilters,
    availableGroups,
    availableRoles,
    onRoleFilterChange,
    onGroupFilter<PERSON>hange,
    onGroupFilterClear,
    onAddMember,
    selectedCount = 0,
    onDelete,
  }: MembersFiltersProps) => {
    const { t } = useTranslation()

    const groupsForFilter = [
      { id: '-1', name: t('members.general') },
      ...availableGroups,
    ]

    return (
      <div className="flex items-center justify-between px-1">
        <div className="flex items-center gap-[12px]">
          <SearchBar
            onSearch={onSearch}
            placeholder={t('user_management.search_placeholder')}
            hasFilter
            filterOptions={[
              {
                contentClassName: 'w-auto',
                overlayClassName: '[--anchor-gap:0px]',
                placement: 'bottom',
                value: selectedRoleFilter,
                data: availableRoles,
                onChangeSelectedValue: onRoleFilterChange,
                placeholder: t('members.filter_role'),
              },
              {
                contentClassName: 'max-w-[200px] max-h-[216px] w-auto pr-1',
                overlayClassName: '[--anchor-gap:0px]',
                placement: 'bottom',
                value: selectedGroupFilters,
                multipleChoice: true,
                allowClear: selectedGroupFilters.length > 0,
                data: groupsForFilter,
                onClear: onGroupFilterClear,
                onChangeSelectedValue: onGroupFilterChange,
                placeholder: t('members.filter_group'),
              },
            ]}
          />

          {selectedCount > 0 && onDelete && (
            <button
              onClick={onDelete}
              className="flex items-center justify-center gap-[7px] rounded-md px-2 py-1"
            >
              <IconTrash01 />
              <Text type="subBody" variant="regular" className="text-red-700">
                {t('members.delete')}
              </Text>
            </button>
          )}
        </div>

        <Button
          type="primary"
          text={t('members.add_member')}
          className="px-[12px] py-[5.5px]"
          onClick={onAddMember}
        />
      </div>
    )
  }
)

MembersFilters.displayName = 'MembersFilters'

export default MembersFilters
