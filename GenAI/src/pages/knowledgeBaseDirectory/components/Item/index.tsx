import { KBFilePublic } from '@/apis/client'
import Dropdown, { SelectedItemProps } from '@/components/Dropdown'
import More from '@/components/More'
import Text from '@/components/Text'
import { EFileType } from '@/components/Upload/const'
import { getFileTypeByMime } from '@/components/Upload/helper'
import IconFile from '@/components/Upload/IconFile'
import clsx from 'clsx'
import { useCallback, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { twMerge } from 'tailwind-merge'
import { useDebouncedCallback } from 'use-debounce'
import { EKBFileEmbeddingStatus } from '../../consts'
import { formatTime } from '../../helper'

import { formatBytes, removeSpaceBetween } from '@/helpers'
import './styles.css'

interface Props {
  item: KBFilePublic
  onClickFailed?: (item: KBFilePublic) => void
  onEdit: (id: string) => void
  onDelete: (item: KBFilePublic) => void
}

const Item = ({ item, onClickFailed, onEdit, onDelete }: Props) => {
  const { t } = useTranslation()
  const fileExt = getFileTypeByMime(item.file_extension)
  const isEmbedding =
    item.embedding_status === EKBFileEmbeddingStatus.Processing
  const isFailed =
    item.embedding_status === EKBFileEmbeddingStatus.New ||
    item.embedding_status === EKBFileEmbeddingStatus.Failed

  const [openWorkerAction, setOpenWorkerAction] = useState(false)

  const handleSelectAction = useCallback(({ key }: SelectedItemProps) => {
    switch (key) {
      case 'edit':
        onEdit(item.id)
        break
      case 'delete':
        onDelete(item)
        break
    }
  }, [])

  const handleClickFailed = useDebouncedCallback(
    () => {
      onClickFailed?.(item)
    },
    600,
    {
      leading: true,
      trailing: false,
    }
  )

  return (
    <div className={twMerge('w-full')}>
      <div
        className={twMerge(
          'relative flex h-[93px] w-full items-center justify-between rounded-[20px] bg-white p-[16px] shadow-base',
          clsx({
            'border border-Error-Color': isFailed,
          })
        )}
      >
        <div className="flex w-full max-w-[calc(100%-212px)] items-center gap-[12px]">
          <div className="flex h-[67px] w-fit flex-col items-center gap-[4px]">
            <IconFile size={48} fileExt={fileExt} />

            <Text
              type="supportText"
              variant="medium"
              className="text-Secondary-Color"
            >
              {removeSpaceBetween(
                `${formatBytes({ bytes: item?.file_size ?? 0 })}`
              )}
            </Text>
          </div>

          <div className="flex h-[69px] w-fit max-w-full flex-col justify-center gap-[4px]">
            <Text
              type="body"
              variant="medium"
              className="overflow-hidden text-Primary-Color"
              elementType="div"
              hasTooltip={false}
              ellipsis
            >
              {item.file_display_name}
            </Text>
            <Text
              type="subBody"
              variant="regular"
              className="overflow-hidden break-words text-Secondary-Color"
              elementType="div"
              multipleLine={2}
              hasTooltip={false}
              ellipsis
            >
              {item.file_description}
            </Text>
          </div>

          <div
            className={twMerge(
              'flex h-[20px] w-auto items-center justify-center rounded-full px-[8px] pb-[1px]',
              clsx({
                'bg-emerald-50': fileExt === EFileType.csv,
                'bg-red-50': fileExt === EFileType.pdf,
                'bg-blue-50':
                  fileExt === EFileType.doc || fileExt === EFileType.docx,
                'bg-black/5': fileExt === EFileType.txt,
                'bg-orange-50':
                  fileExt === EFileType.ppt || fileExt === EFileType.pptx,
                'bg-[#E7F1D1]': fileExt === EFileType.epub,
              })
            )}
          >
            <Text
              type="subBody"
              variant="medium"
              className={clsx({
                'text-emerald-700': fileExt === EFileType.csv,
                'text-red-700': fileExt === EFileType.pdf,
                'text-blue-600':
                  fileExt === EFileType.doc || fileExt === EFileType.docx,
                'text-black': fileExt === EFileType.txt,
                'text-orange-600':
                  fileExt === EFileType.ppt || fileExt === EFileType.pptx,
                'text-[#74A111]': fileExt === EFileType.epub,
              })}
            >
              {fileExt}
            </Text>
          </div>

          <div
            className={
              'flex h-[20px] w-auto items-center justify-center gap-[4px] rounded-full bg-violet-50 px-[8px] py-[2px]'
            }
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="14"
              height="14"
              viewBox="0 0 14 14"
              fill="none"
            >
              <path
                d="M11.0833 8.63324C11.0833 8.13368 11.0831 7.79404 11.0617 7.53151C11.046 7.33983 11.0206 7.21829 10.9893 7.13104L10.9563 7.05357C10.8584 6.86151 10.7095 6.70081 10.5268 6.58872L10.4465 6.54372C10.3552 6.49724 10.2242 6.45923 9.96851 6.43833C9.70599 6.41689 9.36634 6.41669 8.86678 6.41669H5.13322C4.63366 6.41669 4.29402 6.41689 4.0315 6.43833C3.83981 6.454 3.71827 6.47947 3.63102 6.51068L3.55355 6.54372C3.3615 6.64159 3.20079 6.79052 3.08871 6.97325L3.0437 7.05357C2.99722 7.1448 2.95921 7.27577 2.93831 7.53151C2.91687 7.79404 2.91667 8.13368 2.91667 8.63324V9.45013L2.91952 10.0899C2.92231 10.27 2.92759 10.4206 2.93831 10.5519C2.95921 10.8076 2.99722 10.9386 3.0437 11.0298L3.08871 11.1101C3.20079 11.2929 3.3615 11.4418 3.55355 11.5397L3.63102 11.5727C3.71827 11.6039 3.83981 11.6294 4.0315 11.645C4.29402 11.6665 4.63366 11.6667 5.13322 11.6667H8.86678C9.36634 11.6667 9.70599 11.6665 9.96851 11.645C10.2242 11.6241 10.3552 11.5861 10.4465 11.5397L10.5268 11.4946C10.7095 11.3826 10.8584 11.2219 10.9563 11.0298L10.9893 10.9523C11.0206 10.8651 11.046 10.7435 11.0617 10.5519C11.0831 10.2893 11.0833 9.94969 11.0833 9.45013V8.63324ZM6.41667 9.62502V8.45835C6.41667 8.13619 6.67783 7.87502 7 7.87502C7.32217 7.87502 7.58333 8.13619 7.58333 8.45835V9.62502C7.58333 9.94719 7.32217 10.2084 7 10.2084C6.67783 10.2084 6.41667 9.94719 6.41667 9.62502ZM12.25 9.45013C12.25 9.9305 12.2506 10.3259 12.2244 10.647C12.2009 10.9339 12.1541 11.2006 12.0461 11.4525L11.9959 11.5596C11.8002 11.9437 11.5023 12.2651 11.1369 12.4893L10.9762 12.5793C10.6931 12.7235 10.3915 12.7809 10.0636 12.8077C9.74259 12.834 9.34715 12.8334 8.86678 12.8334H5.13322C4.65285 12.8334 4.25742 12.834 3.93636 12.8077C3.64945 12.7843 3.3827 12.7374 3.13086 12.6294L3.02376 12.5793C2.63966 12.3835 2.31825 12.0857 2.09408 11.7202L2.00407 11.5596C1.85983 11.2765 1.80243 10.9749 1.77564 10.647C1.76252 10.4864 1.75601 10.3072 1.75285 10.1081L1.75 9.45013V8.63324C1.75 8.15287 1.7494 7.75744 1.77564 7.43638C1.80243 7.10851 1.85983 6.80688 2.00407 6.52378L2.09408 6.36314C2.31825 5.99768 2.63966 5.69983 3.02376 5.50409L3.13086 5.45396C3.2503 5.40272 3.37332 5.36609 3.5 5.33832V4.66669C3.5 2.73369 5.067 1.16669 7 1.16669C8.3913 1.16669 9.59174 1.97844 10.1559 3.15196L10.2083 3.26646L10.2294 3.32229C10.3199 3.60379 10.1843 3.91339 9.90755 4.03436C9.63078 4.15529 9.31099 4.04472 9.16585 3.78713L9.13965 3.73358L9.06673 3.58205C8.67575 2.83871 7.89627 2.33335 7 2.33335C5.71134 2.33335 4.66667 3.37802 4.66667 4.66669V5.25173C4.81239 5.25073 4.96776 5.25002 5.13322 5.25002H8.86678C9.34715 5.25002 9.74259 5.24942 10.0636 5.27565C10.3915 5.30245 10.6931 5.35985 10.9762 5.50409L11.1369 5.5941C11.5023 5.81826 11.8002 6.13968 11.9959 6.52378L12.0461 6.63088C12.1541 6.88272 12.2009 7.14947 12.2244 7.43638C12.2506 7.75744 12.25 8.15287 12.25 8.63324V9.45013Z"
                fill="#8B5CF6"
              />
            </svg>
            <Text type="subBody" variant="medium" className={'text-violet-500'}>
              4 groups
            </Text>
          </div>
        </div>

        {!isEmbedding && !isFailed && (
          <div className="flex h-full w-auto flex-col justify-end">
            <Text
              type="subBody"
              variant="medium"
              className="w-max text-Secondary-Color"
            >
              {formatTime(item.created_at)}
            </Text>
          </div>
        )}

        {!isEmbedding && (
          <div className="absolute right-[6px] top-[6px] flex flex-row-reverse gap-1">
            <Dropdown
              // overlayClassName="w-[71px]"
              open={openWorkerAction}
              placement="bottom end"
              items={[
                {
                  key: 'edit',
                  label: t('knowledge_base.edit'),
                },
                {
                  key: 'delete',
                  label: t('knowledge_base.delete'),
                },
              ]}
              onSelect={(key) => handleSelectAction(key)}
              onOpenChange={(newOpen) => {
                setOpenWorkerAction(newOpen)
              }}
            >
              <More active={openWorkerAction} />
            </Dropdown>
          </div>
        )}

        {isEmbedding && (
          <div className="flex h-full w-auto select-none items-end justify-center px-[4px]">
            <Text
              type="subBody"
              variant="medium"
              className="w-max text-Secondary-Color"
            >
              {t('knowledge_base.processing')}
            </Text>
          </div>
        )}

        {isFailed && (
          <div className="flex h-full w-auto select-none items-end justify-center px-[4px]">
            <Text
              type="subBody"
              variant="semibold"
              className="w-max cursor-pointer text-Error-Color duration-300 hover:underline hover:decoration-solid hover:decoration-[1px] hover:underline-offset-[2.5px]"
              onClick={handleClickFailed}
            >
              {t('knowledge_base.retry_process')}
            </Text>
          </div>
        )}
      </div>
    </div>
  )
}

export default Item
