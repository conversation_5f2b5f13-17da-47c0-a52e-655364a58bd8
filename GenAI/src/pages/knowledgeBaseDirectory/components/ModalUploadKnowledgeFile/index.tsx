import { knowledgeBaseFileCreateKbFilesApi } from '@/apis/client'
import { MessageDialog } from '@/components/DialogMessage'
import Input from '@/components/Input'
import Message from '@/components/Message'
import Modal from '@/components/Modal'
import TextArea from '@/components/TextArea'
import UploadFile from '@/components/Upload/UploadFile'
import i18n from '@/i18n'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'

const ERR_FILE_EXISTED = i18n.t('knowledge_base.file_name_already_exists')
const ERR_NOT_ENOUGH_STORAGE = i18n.t(
  'knowledge_base.not_enough_storage_available_to_complete'
)
interface Props {
  kbDirectoryId?: string
  open?: boolean
  setIsWaitingNewData: React.Dispatch<React.SetStateAction<boolean>>
  onClose?: () => void
  onUploadSuccess?: () => void
}
export const ModalUploadKnowledgeFile = ({
  open,
  kbDirectoryId,
  setIsWaitingNewData,
  onClose,
  onUploadSuccess,
}: Props) => {
  const { t } = useTranslation()
  const [displayName, setDisplayName] = useState('')
  const [description, setDescription] = useState('')
  const [targetFile, setTargetFile] = useState<File>()
  const [isUploadFailed, setIsUploadFailed] = useState(false)
  const [isUploading, setIsLoading] = useState(false)
  const [errorDisplayName, setErrorDisplayName] = useState<string>()
  const [errorFile, setErrorFile] = useState<string>()

  const handleChangeDisplayName = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value

    setDisplayName(value)
  }

  const handleChangeDescription = (value: string) => {
    setDescription(value)
  }

  const handleChangeFile = (file: File | undefined) => {
    if (isUploadFailed) {
      setIsUploadFailed(false)
    }

    setTargetFile(file)
  }

  const handleOk = async () => {
    if (!kbDirectoryId || !targetFile) return

    if (errorDisplayName) {
      setErrorDisplayName(undefined)
    }
    if (errorFile) {
      setErrorFile('')
      setIsUploadFailed(false)
    }

    try {
      setIsLoading(true)

      setIsWaitingNewData(true)

      const { data, error, status } = await knowledgeBaseFileCreateKbFilesApi({
        path: {
          knowledge_base_directory_id: kbDirectoryId,
        },
        body: {
          file_display_name: displayName.trim(),
          file_description: description.trim(),
          file_storage_upload: targetFile,
        },
      })

      setIsWaitingNewData(false)
      if (status !== 200) {
        if (error && error.detail === ERR_FILE_EXISTED) {
          setErrorDisplayName(ERR_FILE_EXISTED)
          return
        } else if (error && error.detail === ERR_NOT_ENOUGH_STORAGE) {
          setIsUploadFailed(true)
          setErrorFile(ERR_NOT_ENOUGH_STORAGE)
        } else if (status !== 200 && error && error.detail) {
          if (Array.isArray(error.detail))
            setErrorDisplayName(error.detail[0]?.msg)
          else {
            Message.error({
              message: error.detail,
            })
          }
          return
        } else {
          Message.error({
            message: t('common.something_went_wrong'),
          })
        }
      }

      if (status === 200 && data) {
        onUploadSuccess?.()
        setIsLoading(false)
        Message.success({
          message: t('knowledge_base.file_is_being_processed'),
        })
      }
    } catch (error) {
      console.log('🚀 ~ handleOk ~ error:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleClose = () => {
    if (displayName || targetFile || description) {
      MessageDialog.warning({
        mainMessage: t('dialog.wanna_leave.title'),
        subMessage: t('dialog.wanna_leave.description'),
        onClick: () => {
          onClose?.()
        },
      })

      return
    }

    onClose?.()
  }

  return (
    <Modal
      open={open}
      showCloseButton
      title={t('knowledge_base.upload_knowledge_file')}
      subTitle={t('knowledge_base.add_data_source_for_your_worker')}
      okText={isUploadFailed ? t('knowledge_base.retry') : t('common.upload')}
      classNameOkButton="w-[133px] min-w-[133px]"
      classNameCancelButton="invisible"
      classNameFooter="justify-end"
      className="min-h-[502px] w-[672px]"
      okDisable={!displayName || !targetFile || isUploading}
      okLoading={isUploading}
      onClickClose={handleClose}
      onClickOk={handleOk}
    >
      <div className="box-border flex w-[624px] flex-col rounded-[12px] border border-neutral-200 bg-white px-[24px] py-[16px]">
        <div className="box-border flex h-auto flex-col gap-[8px]">
          <div className="px-[4px] text-[14px] font-medium leading-[21px] text-Primary-Color">
            {t('knowledge_base.display_name')}
          </div>

          <Input
            value={displayName}
            onChange={handleChangeDisplayName}
            maxLength={50}
            placeholder={t(
              'knowledge_base.type_in_name_for_your_knowledge_file'
            )}
            errorText={errorDisplayName}
            isError={!!errorDisplayName}
            autoFocus
            onBlur={() => {
              setDisplayName(displayName.trim())
            }}
          />

          <div className="px-[4px] text-[14px] font-medium leading-[21px] text-Primary-Color">
            {t('knowledge_base.description')}
          </div>

          <TextArea
            placeholder={t(
              'knowledge_base.supplementary_information_to_use_knowledge_file'
            )}
            className="h-[63px]"
            textAreaClassName="placeholder:text-subBody placeholder:font-regular"
            value={description}
            onChange={handleChangeDescription}
            onBlur={() => {
              setDescription(description.trim())
            }}
          />

          <div className="px-[4px] text-[14px] font-medium leading-[21px] text-Primary-Color">
            {t('knowledge_base.file')}
          </div>

          <UploadFile
            dataTypes=".csv,.pdf,.doc,.docx,.ppt,.pptx,.txt,.epub"
            className="h-[120px]"
            file={targetFile}
            onChange={handleChangeFile}
            isUploadFailed={isUploadFailed}
            uploadFailedMessage={errorFile}
            isDisabledRemove={isUploading}
          />
        </div>
      </div>
    </Modal>
  )
}
