/* eslint-disable react/jsx-key */
import {
  knowledgeBaseFileUpdateEmbeddingKbFileByIdApi,
  knowledgeBaseFileUploadKbFilesApi,
} from '@/apis/client'
import { MessageDialog } from '@/components/DialogMessage'
import Modal from '@/components/Modal'
import RouteLeaving from '@/components/RouteLeaving'
import { getFileNameWithoutExtension } from '@/components/Upload/helper'
import UploadFile from '@/components/Upload/UploadFile'
import { GEN_AI_INTERNAL_PATH } from '@/constants'
import i18n from '@/i18n'
import clsx from 'clsx'
import { useCallback, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import Item from './components/Item'

const ERR_NOT_ENOUGH_STORAGE = i18n.t(
  'knowledge_base.not_enough_storage_available_to_complete'
)

export enum FileStatus {
  PREVIEW = 'preview',
  UPLOADING = 'uploading',
  UPLOADED = 'uploaded',
  ERROR = 'error',
}

export type FileUpload = {
  rawFile: File
  displayName: string
  description?: string
  status: FileStatus
  error?: boolean
  knowledgeFileId?: string
}

interface Props {
  kbDirectoryId?: string
  open?: boolean
  setIsWaitingNewData: React.Dispatch<React.SetStateAction<boolean>>
  onClose?: () => void
  onUploadSuccess?: (uploadedFiles: FileUpload[]) => void
}
export const ModalUploadMultipleKnowledgeFile = ({
  open,
  kbDirectoryId,
  setIsWaitingNewData,
  onClose,
  onUploadSuccess,
}: Props) => {
  const { t } = useTranslation()
  const [errorFile, setErrorFile] = useState<string>()
  const [uploadingFiles, setUploadingFiles] = useState<FileUpload[]>([])

  const isLoadingUploadBtn = useMemo(() => {
    if (uploadingFiles?.some((file) => file.status === FileStatus.PREVIEW)) {
      return false
    }

    return uploadingFiles.some((file) => file.status === FileStatus.UPLOADING)
  }, [uploadingFiles])

  const isDisabledUploadBtn = useMemo(() => {
    if (!uploadingFiles?.length) {
      return true
    }

    if (uploadingFiles.some((file) => file.status === FileStatus.PREVIEW)) {
      return false
    }

    return uploadingFiles.some((file) => file.status === FileStatus.UPLOADING)
  }, [uploadingFiles])

  const uploadButtonContent = useMemo(() => {
    if (!uploadingFiles?.length) {
      return t('knowledge_base.upload')
    }
    if (uploadingFiles.some((file) => file.status === FileStatus.PREVIEW)) {
      return t('knowledge_base.upload')
    }

    return t('knowledge_base.continue')
  }, [uploadingFiles])

  const handleChangeFiles = useCallback(
    (files?: File[]) => {
      if (errorFile) {
        setErrorFile(undefined)
      }

      if (!files?.length) {
        return
      }

      const addedFiles: FileUpload[] = [
        ...files.map((file) => ({
          rawFile: file,
          displayName: getFileNameWithoutExtension(file.name),
          description: '',
          status: FileStatus.PREVIEW,
          error: undefined,
          knowledgeFileId: undefined,
        })),
      ]
      setUploadingFiles((prevFiles) => [...addedFiles, ...prevFiles])
    },
    [errorFile]
  )

  const handleDeleteFiles = useCallback((files: FileUpload[]) => {
    setUploadingFiles((prevFiles) =>
      prevFiles.filter((file) => !files.includes(file))
    )
  }, [])

  const handleChangeFileName = useCallback(({ file, name }: any) => {
    file.displayName = name
    setUploadingFiles((prevFiles) => [...prevFiles])
  }, [])

  const handleChangeFileDesc = useCallback(({ file, description }: any) => {
    file.description = description
    setUploadingFiles((prevFiles) => [...prevFiles])
  }, [])

  const handleChangeFileStatus = useCallback(({ file, status }: any) => {
    file.status = status
    setUploadingFiles((prevFiles) => [...prevFiles])
  }, [])

  const handleUploadFile = useCallback(
    async (file: FileUpload) => {
      if (!kbDirectoryId || !file) return

      const {
        rawFile,
        displayName,
        description,
        status: fileStatus,
        error: fileError,
      } = file

      try {
        if (fileError) {
          file.error = undefined
        }
        if (fileStatus !== FileStatus.UPLOADING) {
          handleChangeFileStatus({
            file,
            status: FileStatus.UPLOADING,
          })
        }

        const { data, error, status } = await knowledgeBaseFileUploadKbFilesApi(
          {
            baseURL: GEN_AI_INTERNAL_PATH,
            path: {
              knowledge_base_directory_id: kbDirectoryId,
            },
            body: {
              file_display_name: displayName.trim(),
              file_description: description?.trim(),
              file_storage_upload: rawFile,
            },
          }
        )

        if (status !== 200) {
          if (
            error &&
            (error.detail as unknown as string) === ERR_NOT_ENOUGH_STORAGE
          ) {
            setErrorFile(ERR_NOT_ENOUGH_STORAGE)
          }

          handleChangeFileStatus({
            file,
            status: FileStatus.ERROR,
          })
        }

        if (status === 200 && data) {
          // Saving kb file id
          file.knowledgeFileId = data.data.id
          handleChangeFileStatus({
            file,
            status: FileStatus.UPLOADED,
          })
        }
      } catch (error) {
        console.log('error:', error)
      }
    },
    [kbDirectoryId]
  )

  const handleUploadFiles = useCallback(async () => {
    if (errorFile) {
      setErrorFile(undefined)
    }

    // Filter files by status 'Preview'
    const previewingFiles = uploadingFiles.filter(
      (file) => file.status === FileStatus.PREVIEW
    )

    // Change status into Uploading
    uploadingFiles.map((file) => {
      const { status, error } = file
      if (status !== FileStatus.PREVIEW) {
        return file
      }

      if (error) {
        file.error = undefined
      }
      file.status = FileStatus.UPLOADING

      return { ...file }
    })
    setUploadingFiles((prevFiles) => [...prevFiles])

    setIsWaitingNewData(true)

    await new Promise((resolve) => {
      previewingFiles.forEach(async (file) => await handleUploadFile(file))
      resolve(true)
    }).then(() => {
      setIsWaitingNewData(false)
    })
  }, [errorFile, uploadingFiles, handleUploadFile, setIsWaitingNewData])

  const handleRetry = useCallback(
    async (file: FileUpload) => {
      if (!file?.knowledgeFileId || !kbDirectoryId) return

      try {
        await knowledgeBaseFileUpdateEmbeddingKbFileByIdApi({
          path: {
            knowledge_base_directory_id: kbDirectoryId,
            KB_file_id: file.knowledgeFileId,
          },
        })
      } catch (error) {
        console.log(error)
      }
    },
    [kbDirectoryId]
  )

  const handleContinue = useCallback(async () => {
    if (errorFile) {
      setErrorFile(undefined)
    }

    const uploadedFiles = uploadingFiles.filter(
      (file) => file.knowledgeFileId && file.status === FileStatus.UPLOADED
    )

    if (uploadedFiles?.length) {
      uploadedFiles.forEach(async (file) => await handleRetry(file))
      onUploadSuccess?.(uploadedFiles)
    } else if (
      uploadingFiles?.some((file) => file?.status === FileStatus.ERROR)
    ) {
      onClose?.()
    }
  }, [errorFile, uploadingFiles, onUploadSuccess, handleRetry, onClose])

  const handleClose = useCallback(() => {
    if (
      uploadingFiles?.some(
        (file) =>
          file.status === FileStatus.PREVIEW ||
          file.status === FileStatus.UPLOADING
      )
    ) {
      MessageDialog.warning({
        mainMessage: t('dialog.wanna_leave.title'),
        subMessage: t('dialog.wanna_leave.description'),
        onClick: () => {
          handleContinue()
          onClose?.()
        },
      })

      return
    }

    handleContinue()
    onClose?.()
  }, [handleContinue, onClose])

  return (
    <>
      <RouteLeaving
        isChanged={uploadingFiles?.some(
          (file) => file.status === FileStatus.UPLOADED
        )}
      />
      <Modal
        open={open}
        showCloseButton
        title={t('knowledge_base.upload_knowledge_file')}
        subTitle={t('knowledge_base.add_data_source_for_your_worker')}
        okText={uploadButtonContent}
        classNameOkButton="w-[133px] min-w-[133px]"
        classNameCancelButton="invisible"
        classNameFooter="h-full justify-end"
        classNameContent="h-full"
        className="h-full w-[60vw] min-w-[600px] max-w-[1057px]"
        classNameHeader="h-full"
        okDisable={isDisabledUploadBtn}
        okLoading={isLoadingUploadBtn}
        onClickClose={handleClose}
        onClickOk={() => {
          uploadButtonContent === t('knowledge_base.upload')
            ? handleUploadFiles()
            : handleContinue()
        }}
      >
        <div
          className={clsx(
            'box-border flex h-full flex-col',
            uploadingFiles?.length && 'gap-[8px]'
          )}
        >
          <UploadFile
            multiple
            size="large"
            dataTypes=".csv,.pdf,.doc,.docx,.ppt,.pptx,.txt"
            className={clsx(
              'min-h-[240px] duration-500',
              uploadingFiles?.length && '!min-h-[150px]'
            )}
            onChange={handleChangeFiles}
            isUploadFailed={!!errorFile}
            uploadFailedMessage={errorFile}
          />
          <div
            className={clsx(
              'genai-scrollbar flex flex-col gap-1 overflow-auto',
              uploadingFiles?.length && 'max-h-[calc(100vh-400px)] min-h-[60px]'
            )}
          >
            {uploadingFiles.map((file) => {
              return (
                <Item
                  file={file}
                  onChangeFileName={(value) =>
                    handleChangeFileName({
                      file,
                      name: value,
                    })
                  }
                  onChangeFileDescription={(value) =>
                    handleChangeFileDesc({
                      file,
                      description: value,
                    })
                  }
                  onDelete={(file) => handleDeleteFiles([file])}
                  onRetry={(file) => handleUploadFile(file)}
                />
              )
            })}
          </div>
        </div>
      </Modal>
    </>
  )
}
