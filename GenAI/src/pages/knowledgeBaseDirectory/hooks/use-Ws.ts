import { usersReadCurrentUser } from '@/apis/client'
import { noop } from 'lodash'
import { useEffect, useRef } from 'react'

const TIME_TO_RECONNECT = 5000
const MAX_RECONNECT_ATTEMPTS = 3

export const useWs = ({
  url,
  allowRefetchToken = true,
  onMessage,
  onClose,
}: {
  url: string
  allowRefetchToken?: boolean
  onMessage?: (event: MessageEvent) => void
  onClose?: (event: CloseEvent) => void
}) => {
  const ws = useRef<WebSocket>()
  const mounted = useRef(true)
  const reconnectAttempts = useRef(0)

  useEffect(() => {
    const connect = () => {
      if (ws.current) {
        ws.current.close()
      }

      const socket = new WebSocket(url)
      ws.current = socket

      socket.onopen = () => {
        reconnectAttempts.current = 0
        console.log('Connected to ws')
      }
      socket.onclose = (event) => {
        console.log('Closed ws', event)

        if (allowRefetchToken && event.code === 3401) {
          usersReadCurrentUser()
        }

        ws.current = undefined

        onClose?.(event)

        // Reconnect
        if (mounted.current) {
          if (reconnectAttempts.current < MAX_RECONNECT_ATTEMPTS) {
            reconnectAttempts.current++

            setTimeout(() => {
              connect()
            }, TIME_TO_RECONNECT)
          }
        }
      }
      socket.onmessage = onMessage ?? noop

      return socket
    }

    const s = connect()

    return () => {
      mounted.current = false
      s.close()
    }
  }, [])

  return {
    send: ws.current?.send.bind(ws.current),
  }
}
