import { usersDeleteMultiple<PERSON>sers<PERSON><PERSON>, usersRead<PERSON><PERSON>s<PERSON><PERSON> } from '@/apis/client'
import Icon from '@/assets/icon/Icon'
import BaseModal from '@/components/BaseModal'
import { MessageDialog } from '@/components/DialogMessage'
import EmptyData from '@/components/EmptyData'
import { AccessDenied } from '@/components/ErrorPage'
import LayoutSettings from '@/components/LayoutSettings'
import Message from '@/components/Message'
import NoDataFound from '@/components/NoDataFound'
import SearchBar from '@/components/SearchBar'
import { ISelectBaseItem } from '@/components/SearchBar/components/Select'
import Table from '@/components/Table'
import Text from '@/components/Text'
import { DEBOUNCE_TIME, HTTP_STATUS_CODE } from '@/constants'
import { useAuth } from '@/hooks/useAuth'
import { useMyProfile } from '@/hooks/useMyProfile'
import { rootUrls } from '@/routes/rootUrls'
import { colors } from '@/theme'
import { debounce } from 'lodash'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router-dom'
import Action from './components/Action'
import MultipleDelete from './components/MultipleDelete'
import Status from './components/Status'
import UserInfo from './components/UserInfo'
import {
  ALL_ORGANIZATIONS,
  ALL_ROLE,
  ALL_STATUS,
  INIT_USER_PARAMS,
  LIST_ROLE,
  STATUS_TYPES,
} from './constants'
import { handleUser } from './helpers'
import { UserParam } from './types'

const UserManagement = () => {
  const tableRef = useRef<HTMLDivElement>(null)
  const navigate = useNavigate()
  const {
    myProfile,
    isSuperAdmin,
    loadingProfile,
    fetchMyProfile,
    fetchMySubscriptionPlan,
  } = useMyProfile()
  const { loginAsUser } = useAuth()
  const { t } = useTranslation()

  const [loading, setLoading] = useState(false)
  const [params, _setParams] = useState<UserParam>({
    ...INIT_USER_PARAMS,
    organizations: [
      { ...ALL_ORGANIZATIONS, name: t(ALL_ORGANIZATIONS.name ?? '') },
    ],
    role: { ...ALL_ROLE, name: t(ALL_ROLE.name ?? '') },
    isActive: { ...ALL_STATUS, name: t(ALL_STATUS.name ?? '') },
  })
  const [organizationLoading, setOrganizationLoading] = useState(false)
  const [organizations, setOrganizations] = useState<ISelectBaseItem[]>([])

  const [users, setUsers] = useState<any[]>([])
  const [userTotal, setUserTotal] = useState(0)
  const [selectedUsers, setSelectedUsers] = useState<number[]>([])

  const [openLoadingLogin, setOpenLoadingLogin] = useState(false)
  const [userLogin, setUserLogin] = useState<any>(null)

  const [openDeletedUser, setOpenDeletedUser] = useState(false)
  const [deletedUserLoading, setDeletedUserLoading] = useState(false)
  const [deletedUsers, setDeletedUsers] = useState<number[]>([])

  const setParams = useCallback((newParams: Partial<typeof params>) => {
    _setParams((prev) => ({
      ...prev,
      ...newParams,
    }))
  }, [])

  const handleChangeSearch = useCallback((value: string) => {
    setParams({
      search: value,
      page: 1,
    })
  }, [])

  const handleChangeOrganization = useCallback(
    (value: ISelectBaseItem) => {
      const isSelected = params.organizations.find(
        (item) => item.id === value.id
      )

      if (isSelected) {
        const newVal = params.organizations.filter(
          (item) => item.id !== value.id
        )

        setParams({
          organizations:
            newVal.length === 0
              ? [
                  {
                    ...ALL_ORGANIZATIONS,
                    name: t(ALL_ORGANIZATIONS.name ?? ''),
                  },
                ]
              : newVal,
        })
      } else {
        setParams({
          organizations: [...params.organizations, value].filter(
            (item) => item.id !== ALL_ORGANIZATIONS.id
          ),
        })
      }
    },
    [t, params.organizations]
  )

  const handleChangeRole = useCallback(
    (value: ISelectBaseItem) => {
      setParams({
        role: value ?? { ...ALL_ROLE, name: t(ALL_ROLE.name ?? '') },
        page: 1,
      })
    },
    [t]
  )

  const handleChangeStatus = useCallback(
    (value: ISelectBaseItem) => {
      setParams({
        isActive: value ?? { ...ALL_STATUS, name: t(ALL_STATUS.name ?? '') },
        page: 1,
      })
    },
    [t]
  )

  const debouncedSearch = useCallback(
    debounce((value: string) => {
      handleChangeSearch?.(value.trim())
    }, DEBOUNCE_TIME),
    []
  )

  const fetchOrganizations = useCallback(async () => {
    setOrganizationLoading(true)
    try {
      setTimeout(() => {
        setOrganizations((prev) => prev)
        setOrganizationLoading(false)
      }, 1000)
    } catch (error) {
      setOrganizationLoading(false)
    }
  }, [setParams])

  const fetchUsers = useCallback(
    async (isRefresh = false) => {
      try {
        const query: any = {
          page_number: isRefresh ? 1 : params.page,
          page_size: params.pageSize,
          is_deleted: false,
          email_confirmed: true,
        }

        if (params.search) {
          query.name = params.search
        }

        if (params.organizations[0]?.id !== ALL_ORGANIZATIONS.id) {
          query.organizations = params.organizations.map((item) => item.id)
        }

        if (params.role?.id !== ALL_ROLE.id) {
          query.role_id = params.role.value
        }

        if (params.isActive?.id !== ALL_STATUS.id) {
          query.is_active = params.isActive.value
        }

        setLoading(true)
        const { data, status } = await usersReadUsersApi({
          query,
        })

        if (status === HTTP_STATUS_CODE.SUCCESS) {
          const userList = data?.data.data || []
          if (isRefresh) {
            setUsers(userList)
            _setParams((prev) => ({
              ...prev,
              page: 2,
            }))
          } else {
            setUsers((prev) => handleUser(prev, userList))
            _setParams((prev) => ({
              ...prev,
              page: prev.page + 1,
            }))
          }
          setUserTotal(data?.data.total_count || 0)
        } else {
          Message.error({
            message: t('user_management.delete_failed'),
          })
        }
      } catch (error) {
        Message.error({
          message: t('user_management.delete_failed'),
        })
      } finally {
        setLoading(false)
      }
    },
    [t, params]
  )

  const handleDeleteMultipleUsers = useCallback(async () => {
    try {
      if (deletedUserLoading) {
        return
      }

      setDeletedUserLoading(true)
      const { status }: any = await usersDeleteMultipleUsersApi({
        body: {
          user_ids: deletedUsers,
        },
      })

      if (status === HTTP_STATUS_CODE.NO_CONTENT) {
        setUsers((prev) =>
          prev.filter((user) => !deletedUsers.includes(user.id))
        )
        setUserTotal((prev) => prev - deletedUsers.length)
        setSelectedUsers((prev) =>
          prev.filter((id) => !deletedUsers.includes(id))
        )

        setOpenDeletedUser(false)
        setDeletedUsers([])

        Message.success({
          message: t('user_management.delete_success'),
        })
      } else {
        Message.error({
          message: t('user_management.delete_failed'),
        })
      }
    } catch (error) {
      Message.error({
        message: t('user_management.delete_failed'),
      })
    } finally {
      setDeletedUserLoading(false)
    }
  }, [t, deletedUserLoading, deletedUsers])

  const handleDeleteUser = useCallback((userIds: number[]) => {
    setDeletedUsers(userIds)
    setOpenDeletedUser(true)
  }, [])

  const handleLoginUser = useCallback(
    async (user: any) => {
      setUserLogin(user)
      setOpenLoadingLogin(true)

      const { status } = await loginAsUser({ userId: user.id })

      if (status === HTTP_STATUS_CODE.SUCCESS) {
        setTimeout(() => {
          setOpenLoadingLogin(false)
          navigate(rootUrls.Home)
          fetchMyProfile()
          fetchMySubscriptionPlan()
        }, 1000)
      } else {
        setOpenLoadingLogin(false)
        Message.error({
          message: t('user_management.login_failed'),
        })
      }
    },
    [t]
  )

  // const handleSelectUsers = useCallback((selectedRowKeys: React.Key[]) => {
  //   setSelectedUsers(selectedRowKeys as number[])
  // }, [])

  // const rowSelection: TableProps<any>['rowSelection'] = useMemo(
  //   () => ({
  //     type: 'checkbox',
  //     columnWidth: 40,
  //     fixed: true,
  //     onChange: handleSelectUsers,
  //   }),
  //   [handleSelectUsers]
  // )

  useEffect(() => {
    fetchOrganizations()
  }, [])

  useEffect(() => {
    const tableBody = tableRef.current?.querySelector('.ant-table-body')
    if (tableBody) {
      tableBody.scrollTop = 0
    }
    fetchUsers(true)
  }, [params.search, params.organizations, params.role, params.isActive])

  const isQuery = useMemo(() => {
    return (
      params.search ||
      params.organizations[0].id !== ALL_ORGANIZATIONS.id ||
      params.role.id !== ALL_ROLE.id ||
      params.isActive.id !== ALL_STATUS.id
    )
  }, [params])

  return (
    <LayoutSettings>
      {isSuperAdmin ? (
        <>
          <div className="flex h-full w-full flex-col gap-[20px] bg-Base-01 px-8">
            <div className="flex h-full flex-col gap-5">
              <div className="flex flex-col gap-1">
                <Text
                  className="text-Primary-Color"
                  type="title"
                  variant="semibold"
                >
                  {t('user_management.title')}
                </Text>
                <Text
                  className="text-Secondary-Color"
                  type="subBody"
                  variant="regular"
                >
                  {t('user_management.description')}
                </Text>
              </div>

              <div className="flex h-full flex-col gap-4" ref={tableRef}>
                {!users?.length && !loading && !isQuery && (
                  <div className="flex h-full w-full items-center justify-center">
                    <EmptyData type="01" />
                  </div>
                )}

                {!(!users?.length && !loading && !isQuery) && (
                  <div className="ml-1 flex items-center gap-3">
                    <SearchBar
                      className="w-fit min-w-[476px]"
                      searchClassName="!w-[260px]"
                      placeholder={t('user_management.search_placeholder')}
                      onSearch={debouncedSearch}
                      filterOptions={[
                        {
                          contentClassName:
                            '!min-w-[123px] w-full max-h-[216px]',
                          overlayClassName: '[--anchor-gap:12px]',
                          data: organizations.filter(
                            (item) => item.id !== ALL_ORGANIZATIONS.id
                          ),
                          selected: params.organizations[0],
                          selectedMultiple: params.organizations,
                          loading: organizationLoading,
                          multipleChoice: true,
                          allowClear:
                            params.organizations[0].id !== ALL_ORGANIZATIONS.id,
                          onClear: () => {
                            if (
                              params.organizations[0].id !==
                              ALL_ORGANIZATIONS.id
                            ) {
                              setParams({
                                organizations: [
                                  {
                                    ...ALL_ORGANIZATIONS,
                                    name: t(ALL_ORGANIZATIONS.name ?? ''),
                                  },
                                ],
                              })
                            }
                          },
                          onChangeSelectedValue: handleChangeOrganization,
                        },
                        {
                          contentClassName: '!min-w-[90px] w-full',
                          overlayClassName: '[--anchor-gap:12px]',
                          selected: params.role,
                          data: LIST_ROLE,
                          onChangeSelectedValue: handleChangeRole,
                        },
                        {
                          contentClassName: '!min-w-[98px] w-full',
                          overlayClassName: '[--anchor-gap:12px]',
                          selected: params.isActive,
                          data: STATUS_TYPES.map((item) => ({
                            ...item,
                            name: t(item.name ?? ''),
                          })),
                          onChangeSelectedValue: handleChangeStatus,
                        },
                      ]}
                      hasFilter
                    />

                    {selectedUsers.length > 0 && (
                      <MultipleDelete
                        onClick={() => handleDeleteUser(selectedUsers)}
                      />
                    )}
                  </div>
                )}

                {!users?.length && !loading && isQuery && (
                  <div className="flex h-full w-full items-center justify-center">
                    <NoDataFound />
                  </div>
                )}

                {users?.length > 0 && (
                  <Table
                    rowKey="id"
                    tableLayout="auto"
                    loading={loading}
                    // rowSelection={rowSelection}
                    columns={[
                      {
                        title: (
                          <span className="pl-12">
                            {t('user_management.table_header_name')}
                          </span>
                        ),
                        key: 'name',
                        ellipsis: true,
                        width: 600,
                        render: (record) => (
                          <UserInfo
                            avatarUrl={record.avatar}
                            name={record.first_name}
                            email={record.email}
                          />
                        ),
                      },
                      {
                        title: t('user_management.table_header_role'),
                        key: 'role',
                        width: 300,
                        render: (record) => {
                          if (record.roles?.[0]?.name === 'SA') {
                            return 'System Admin'
                          }
                          return record.roles?.[0]?.name
                            ? record.roles?.[0]?.name
                            : '--'
                        },
                      },
                      {
                        title: t('user_management.table_header_organization'),
                        key: 'organization',
                        width: 600,
                        ellipsis: true,
                        render: (record) =>
                          record.organizations ? (
                            <Text
                              className="flex max-w-full overflow-hidden whitespace-nowrap"
                              elementType="div"
                              ellipsis
                            >
                              {record.organizations?.[0]?.name}
                            </Text>
                          ) : (
                            '--'
                          ),
                      },
                      {
                        title: t('user_management.table_header_status'),
                        dataIndex: 'is_active',
                        key: 'status',
                        width: 300,
                        ellipsis: true,
                        render: (isActive: boolean) => (
                          <Status isActive={isActive} />
                        ),
                      },
                      {
                        title: '',
                        key: 'action',
                        width: 50,
                        ellipsis: true,
                        render: (record) =>
                          record?.id !== myProfile?.id && (
                            <Action
                              hideLogin={!record?.is_active}
                              onDelete={() => handleDeleteUser([record.id])}
                              onLogin={() => handleLoginUser(record)}
                            />
                          ),
                      },
                    ]}
                    dataSource={users}
                    pagination={{ total: userTotal }}
                    loadMoreData={fetchUsers}
                    scroll={{ y: 'calc(100vh - 330px)', x: 1000 }}
                    infiniteScroll
                  />
                )}
              </div>
            </div>
          </div>

          <BaseModal
            isOpen={openLoadingLogin}
            isPureModal
            className="rounded-xl border border-border-base-icon bg-white px-5 py-3 shadow-base"
          >
            <div className="flex items-center gap-2">
              <Icon
                name="Outline-ArrowsAction-Login3"
                color={colors.blue[500]}
              />
              <Text className="text-Primary-Color" type="subheading">
                {t('user_management.login_as_user_loading', {
                  name: userLogin?.first_name || userLogin?.email,
                })}
              </Text>
            </div>
          </BaseModal>

          {openDeletedUser && (
            <MessageDialog.ConfirmAsync
              type="warning"
              open={openDeletedUser}
              isLoadingConfirm={deletedUserLoading}
              mainMessage={
                deletedUsers.length > 1
                  ? t('user_management.warning_multiple_deleted_title')
                  : t('user_management.warning_single_deleted_title')
              }
              subMessage={t('user_management.warning_deleted_description')}
              onClick={handleDeleteMultipleUsers}
              onClose={() => {
                setOpenDeletedUser(false)
                setDeletedUsers([])
              }}
            />
          )}
        </>
      ) : (
        !loadingProfile && <AccessDenied />
      )}
    </LayoutSettings>
  )
}

export default UserManagement
