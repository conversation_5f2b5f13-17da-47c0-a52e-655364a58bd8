import {
  organizationsCreateOrganizationGroupsApi,
  organizationsDeleteOrganizationGroupsApi,
  organizationsReadOrganizationByIdApi,
  organizationsUpdateOrganizationApi,
  organizationsUpdateOrganizationGroupsApi,
} from '@/apis/client'
import Button from '@/components/Button'
import { AccessDenied } from '@/components/ErrorPage'
import Input from '@/components/Input'
import LayoutSettings from '@/components/LayoutSettings'
import Message from '@/components/Message'
import Text from '@/components/Text'
import Upload from '@/components/Upload'
import { HTTP_STATUS_CODE } from '@/constants'
import { useOrg } from '@/hooks/useOrg'
import { memo, useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import SimpleBar from 'simplebar-react'
import { GroupStatus } from '../const'
import { IGroup } from '../types'
import DeleteOrg from './DeleteOrg'
import Groups from './Groups'

const Information = () => {
  const { t } = useTranslation()

  const { listOrgs, fetchMyOrgs, isOrgOwner, isFetchingOrg } = useOrg()

  const myOrg = useMemo(() => {
    return listOrgs[0]
  }, [listOrgs])
  const myOrgId = useMemo(() => {
    return listOrgs[0]?.id
  }, [listOrgs])

  const [logo_file, setLogoFile] = useState<File>()
  const [logo, setLogo] = useState<string>()
  const [orgName, setOrgName] = useState<string>('')
  const [groups, setGroups] = useState<IGroup[]>([])

  const [isDirtyData, setDirtyData] = useState<boolean>(false)

  const [isLoading, setLoading] = useState<boolean>(false)

  const fetchOrgInformation = useCallback(async () => {
    try {
      if (isLoading || !myOrgId) return
      setLoading(true)

      const res = await organizationsReadOrganizationByIdApi({
        path: {
          organization_id: myOrgId,
        },
      })

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        const orgInfo = res?.data?.data

        setLogo(orgInfo?.avatar || '')
        setOrgName(orgInfo?.name || '')
        setGroups(orgInfo?.organization_groups || [])
      }
    } catch (error) {
      Message.error({ message: t('common.something_went_wrong') })
    } finally {
      setLoading(false)
    }
  }, [myOrgId])

  useEffect(() => {
    fetchOrgInformation()
  }, [myOrgId])

  const handleSaveChanges = useCallback(async () => {
    if (!isDirtyData) return

    try {
      setLoading(true)

      const listNewGroups = groups?.filter((g) => g.status === GroupStatus.NEW)
      const listDeleteGroups = groups?.filter(
        (g) => g.status === GroupStatus.DELETED
      )
      const listEditedGroups = groups?.filter(
        (g) => g.status === GroupStatus.EDITED
      )

      if (listNewGroups?.length) {
        await organizationsCreateOrganizationGroupsApi({
          path: {
            organization_id: myOrgId,
          },
          body: listNewGroups.map((g) => ({
            name: g.name,
          })),
        })
      }

      if (listEditedGroups?.length) {
        await organizationsUpdateOrganizationGroupsApi({
          path: {
            organization_id: myOrgId,
          },
          body: listEditedGroups.map((g) => ({
            id: g.id,
            name: g.name,
          })),
        })
      }

      if (listDeleteGroups?.length) {
        await organizationsDeleteOrganizationGroupsApi({
          path: {
            organization_id: myOrgId,
          },
          body: listDeleteGroups.map((g) => g.id),
        })
      }

      if (orgName !== myOrg?.name || logo_file || logo !== myOrg?.avatar) {
        const res = await organizationsUpdateOrganizationApi({
          path: { organization_id: myOrgId },
          body: {
            name: orgName,
            logo_file,
            logo: logo_file ? '' : (logo ?? ''), // If user upload new logo => set logo to empty string
          },
        })

        if (res.status === HTTP_STATUS_CODE.SUCCESS) {
          Message.success({ message: t('org_info.update_org_info') })
          fetchMyOrgs()
          fetchOrgInformation()
          setLogoFile(undefined)
        } else {
          Message.error({ message: t('common.something_went_wrong') })
        }
      } else {
        Message.success({ message: t('org_info.update_org_info') })
        fetchMyOrgs()
        fetchOrgInformation()
      }
    } catch (error) {
      Message.error({ message: t('common.something_went_wrong') })
    } finally {
      setDirtyData(false)
      setLoading(false)
    }
  }, [isDirtyData, orgName, groups, logo_file, myOrg, myOrgId, logo, t])

  if (!isFetchingOrg && !isOrgOwner) {
    return (
      <LayoutSettings>
        <AccessDenied />
      </LayoutSettings>
    )
  }

  return (
    <LayoutSettings>
      <div className="flex-grow overflow-hidden">
        <SimpleBar className="size-full">
          <div className="flex w-[631px] flex-col px-8">
            <Text type="title" variant="semibold" className="mb-6">
              {t('sidebarsettings.general')}
            </Text>

            <div className="flex flex-col gap-4 pb-5">
              <Input
                label={t('org.org_name')}
                placeholder={t('org.org_name_placeholder')}
                onChange={(e) => {
                  setOrgName(e.target.value)
                  setDirtyData(true)
                }}
                className="w-full"
                required
                onBlur={(e) => {
                  setOrgName(e.target.value?.trim())
                }}
                value={orgName}
                maxLength={50}
              />

              <div className="flex flex-col gap-1">
                <Text
                  className="text-Tertiary-Color"
                  type="subBody"
                  variant="medium"
                >
                  {t('org.logo')}
                </Text>
                <Upload
                  onChangeFile={(file: File | null) => {
                    if (file) setLogoFile(file)
                  }}
                  image={logo}
                  type="mini"
                  onChange={(image) => {
                    setLogo(image)
                    setDirtyData(true)
                  }}
                  dataTypes="image/png, image/jpeg, image/jpg"
                  size="large"
                />
              </div>
              <Groups
                groups={groups}
                setGroups={setGroups}
                setDirtyData={setDirtyData}
              />

              <div className="flex justify-end">
                <Button
                  onClick={handleSaveChanges}
                  type="primary"
                  size="small"
                  loading={isLoading}
                  disabled={!isDirtyData || !orgName?.trim()}
                >
                  {t('common.save')}
                </Button>
              </div>
            </div>

            <DeleteOrg />
          </div>
        </SimpleBar>
      </div>
    </LayoutSettings>
  )
}

export default memo(Information)
