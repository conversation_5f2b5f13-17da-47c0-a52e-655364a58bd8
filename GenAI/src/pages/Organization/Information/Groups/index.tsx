import { MessageDialog } from '@/components/DialogMessage'
import Input from '@/components/Input'
import Message from '@/components/Message'
import { nanoid } from 'nanoid'
import { Dispatch, SetStateAction, memo, useCallback, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { GroupStatus } from '../../const'
import { IGroup } from '../../types'
import GroupItem from './GroupItem'

interface IProps {
  groups?: IGroup[]
  setGroups: Dispatch<SetStateAction<IGroup[]>>
  setDirtyData?: Dispatch<SetStateAction<boolean>>
}

const Groups = ({ groups, setGroups, setDirtyData }: IProps) => {
  const { t } = useTranslation()

  const [newGroup, setNewGroup] = useState<string>('')

  const handleAddGroup = useCallback(() => {
    if (newGroup?.trim()) {
      if (groups?.some((g) => g.name === newGroup.trim())) {
        Message.error({ message: t('org.group_exists_error') })
        return
      }
      setGroups((prev) => [
        ...(prev || []),
        { name: newGroup.trim(), status: GroupStatus.NEW, id: nanoid() as any }, // TODO: Clean this up later
      ])
      setNewGroup('')
      setDirtyData?.(true)
    }
  }, [t, groups, newGroup])

  const handleRemoveGroup = useCallback(
    (groupId: string | number) => {
      // Remove group if it is new and not saved yet
      if (
        groups?.some((g) => g.id === groupId && g.status === GroupStatus.NEW)
      ) {
        setGroups((prev) => prev?.filter((g) => g.id !== groupId))
        setDirtyData?.(true)
        return
      }

      MessageDialog.warning({
        mainMessage: t('org_info.confirm_delete_group'),
        subMessage: t('org_info.confirm_delete_group.description'),
        onClick: () => {
          // Mark group for deletion if it is already saved
          setGroups((prev) =>
            prev?.map((g) =>
              g.id === groupId ? { ...g, status: GroupStatus.DELETED } : g
            )
          )
          setDirtyData?.(true)
        },
      })
    },
    [t, groups]
  )

  const handleUpdateGroup = useCallback(
    (group: string, newName: string) => {
      if (newName?.trim() && newName !== group) {
        if (groups?.some((g) => g.name === newName.trim())) {
          Message.error({ message: t('org.group_exists_error') })
          return
        }

        // If the group is new and not saved yet, just update the name
        if (
          groups?.some((g) => g.name === group && g.status === GroupStatus.NEW)
        ) {
          setGroups((prev) =>
            prev?.map((g) =>
              g.name === group ? { ...g, name: newName.trim() } : g
            )
          )
          setDirtyData?.(true)
          return
        }

        // Otherwise, mark it for update
        setGroups((prev) =>
          prev?.map((g) =>
            g.name === group
              ? { ...g, name: newName.trim(), status: GroupStatus.EDITED }
              : g
          )
        )
        setDirtyData?.(true)
      }
    },
    [groups, setGroups, setDirtyData, t]
  )

  return (
    <div className="flex flex-col gap-2">
      <Input
        label={t('org.member_group')}
        placeholder={t('org_info.add_new_group.placeholder')}
        value={newGroup}
        onChange={(e) => {
          setNewGroup(e.target.value)
          setDirtyData?.(true)
        }}
        onBlur={(e) => {
          setNewGroup(e.target.value?.trim())
        }}
        onPressEnter={handleAddGroup}
        className="w-full"
        maxLength={50}
      />
      <div className="flex flex-wrap gap-3 p-1">
        {groups
          ?.filter((g) => g.status !== GroupStatus.DELETED)
          ?.map((group) => (
            <GroupItem
              key={group.id}
              group={group}
              handleRemoveGroup={() => handleRemoveGroup(group.id)}
              handleUpdateGroup={handleUpdateGroup}
            />
          ))}
      </div>
    </div>
  )
}

export default memo(Groups)
