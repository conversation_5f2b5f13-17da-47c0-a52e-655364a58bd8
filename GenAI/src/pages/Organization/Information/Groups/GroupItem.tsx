import IconButton from '@/components/IconButton'
import Text from '@/components/Text'
import { colors } from '@/theme'
import clsx from 'clsx'
import { memo, useEffect, useRef, useState } from 'react'
import { IGroup } from '../../types'
import './styles.scss'

interface IGroupItemProps {
  group: IGroup
  handleRemoveGroup?: () => void
  handleUpdateGroup?: (group: string, newName: string) => void
}

const GroupItem = ({
  group,
  handleRemoveGroup,
  handleUpdateGroup,
}: IGroupItemProps) => {
  const [groupName, setGroupName] = useState<string>('')
  const [isEditing, setEditing] = useState<boolean>(false)

  const inpRef = useRef<HTMLInputElement>(null)

  const spanRef = useRef<HTMLSpanElement>(null)

  useEffect(() => {
    if (inpRef.current && spanRef.current) {
      // Update input width based on span width
      inpRef.current!.style.width = `${(spanRef.current!.offsetWidth ?? 0) + 3}px`
    }
  }, [groupName, isEditing])

  // const debouncedChangeGroupName = useCallback(
  //   debounce((value: string) => {
  //     handleUpdateGroup?.(group.name, value.trim())
  //   }, DEBOUNCE_TIME),
  //   []
  // )

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target
    setGroupName(value)
    // debouncedChangeGroupName(value)
  }

  return (
    <div
      className={clsx(
        'flex items-center gap-1.5 rounded-lg border border-border-base-icon bg-white py-2 pl-2 pr-3',
        isEditing && 'group-item-focus'
      )}
    >
      {isEditing ? (
        <>
          <input
            ref={inpRef}
            value={groupName}
            onChange={handleNameChange}
            className={clsx('input-group-name')}
            maxLength={50}
            onBlur={(e) => {
              setEditing(false)
              handleUpdateGroup?.(group.name, e?.target?.value?.trim())
            }}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                setEditing(false)
                handleUpdateGroup?.(group.name, e?.currentTarget?.value?.trim())
              } else if (e.key === 'Escape') {
                setEditing(false)
                setGroupName(group.name)
              }
            }}
            autoFocus
          />

          <span
            ref={spanRef}
            className={clsx(
              'input-group-name hidden-span-responsive-input-group-name'
            )}
          >
            {groupName || ''}
          </span>
        </>
      ) : (
        <Text type="subBody" className="text-Tertiary-Color">
          {group.name}
        </Text>
      )}
      <div className="flex gap-1">
        <IconButton
          nameIcon="edit-02"
          sizeIcon={16}
          colorIcon={colors.neutral[500]}
          className="hover:bg-neutral-100"
          onClick={() => {
            setEditing(true)
            setGroupName(group.name)
          }}
        />
        <IconButton
          nameIcon="x-close"
          sizeIcon={16}
          colorIcon={colors.neutral[500]}
          className="hover:bg-neutral-100"
          onClick={handleRemoveGroup}
        />
      </div>
    </div>
  )
}

export default memo(GroupItem)
