import {
  subscriptionContactSalesApi,
  subscriptionReadSubscriptionsApi,
} from '@/apis/client'
import BaseModal from '@/components/BaseModal'
import IconButton from '@/components/IconButton'
import Message from '@/components/Message'
import { HTTP_STATUS_CODE } from '@/constants'
import { validateEmail } from '@/helpers'
import { useMyProfile } from '@/hooks/useMyProfile'
import { colors } from '@/theme'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { ISubscription, transformSubscriptions } from '../../const'
import ContactForm from '../ContactForm'
import ListPlan from '../ListPlan'
import './style.scss'

const COUNT_DOWN_TIME_RESET_FORM = 1000

interface UpdateSubscriptionProps {
  isOpen: boolean
  onClose: (isForceClose?: boolean) => void
}

const UpdateSubscription = ({ isOpen, onClose }: UpdateSubscriptionProps) => {
  const { t } = useTranslation()

  const [selectedPlan, setSelectedPlan] = useState<string>('Monthly')
  const [plans, setPlans] = useState<{
    month: Array<ISubscription>
    year: Array<ISubscription>
  }>({
    month: [],
    year: [],
  })

  const { myProfile } = useMyProfile()
  const [isContact, setIsContact] = useState<boolean>(false)
  const [contactForm, setContactForm] = useState<{
    name: string
    email: string
    phone: string
    expectations: string
    subscription_name: string
  }>({
    name: '',
    email: '',
    phone: '',
    expectations: '',
    subscription_name: '',
  })
  const [emailError, setEmailError] = useState<string>('')
  const [nameError, setNameError] = useState<string>('')
  const [phoneError, setPhoneError] = useState<string>('')
  const [isSending, setIsSending] = useState<boolean>(false)

  // Reset form and states
  const resetForm = () => {
    setContactForm({
      name: '',
      email: '',
      phone: '',
      expectations: '',
      subscription_name: '',
    })
    setEmailError('')
    setNameError('')
    setPhoneError('')
    setIsContact(false)
  }

  // Handle email validation
  const handleEmailValidation = () => {
    const trimmedEmail = contactForm.email.trim()

    // Update email with trimmed value or empty if all whitespace
    setContactForm({
      ...contactForm,
      email: trimmedEmail,
    })

    // Validate email format if not empty
    if (trimmedEmail && !validateEmail(trimmedEmail)) {
      setEmailError('Invalid email address')
    } else {
      setEmailError('')
    }
  }

  // Validate form before submission
  const validateForm = (): boolean => {
    let isValid = true

    // Validate name
    const trimmedName = contactForm.name.trim()
    if (!trimmedName) {
      setNameError('Required!')
      isValid = false
    } else {
      setNameError('')
    }

    // Validate email
    const trimmedEmail = contactForm.email.trim()
    if (!trimmedEmail) {
      setEmailError('Required!')
      isValid = false
    } else if (!validateEmail(trimmedEmail)) {
      setEmailError('Invalid email address')
      isValid = false
    } else {
      setEmailError('')
    }

    // Validate phone
    const trimmedPhone = contactForm.phone.trim()
    if (!trimmedPhone) {
      setPhoneError('Required!')
      isValid = false
    } else {
      setPhoneError('')
    }

    // Update form with trimmed values
    setContactForm({
      ...contactForm,
      name: trimmedName,
      email: trimmedEmail,
      phone: trimmedPhone,
    })

    return isValid
  }

  const fetchPlans = async () => {
    try {
      const res = await subscriptionReadSubscriptionsApi({
        query: {
          page_number: 1,
          page_size: 100,
          package_type: 'Solution',
        },
      })

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        if (res.data?.data?.data)
          setPlans(transformSubscriptions(res.data?.data?.data))
      }
    } catch (error) {
      Message.error({
        message: t('tool_category.something_wrong_please_retry'),
      })
    }
  }

  useEffect(() => {
    if (isOpen) {
      fetchPlans()
    }
  }, [isOpen])

  const handleContact = (name: string) => {
    setContactForm({
      ...contactForm,
      name: myProfile?.first_name || '',
      email: myProfile?.email || '',
      subscription_name: name,
    })
    setIsContact(true)
  }

  const handleBack = () => {
    if (isContact) {
      setContactForm({
        name: '',
        email: '',
        phone: '',
        expectations: '',
        subscription_name: '',
      })
      setEmailError('')
      setNameError('')
      setPhoneError('')
      setIsContact(false)
      return
    } else {
      onClose(true)
    }
  }

  const handleSendContact = async () => {
    // Validate form before submission
    if (!validateForm()) {
      return
    }

    try {
      setIsSending(true)
      const params: {
        name: string
        email: string
        phone: string
        expectations?: string
        subscription_name: string
      } = {
        name: contactForm.name,
        email: contactForm.email,
        phone: contactForm.phone,
        subscription_name: contactForm.subscription_name,
      }

      if (contactForm.expectations) {
        params.expectations = contactForm.expectations
      }
      const res = await subscriptionContactSalesApi({
        body: params,
      })
      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        onClose()
        setTimeout(resetForm, COUNT_DOWN_TIME_RESET_FORM)
      } else {
        Message.error({
          message: t('tool_category.something_wrong_please_retry'),
        })
      }
    } catch (error) {
      Message.error({
        message: t('tool_category.something_wrong_please_retry'),
      })
    }
    setIsSending(false)
  }

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={handleBack}
      isPureModal
      shouldCloseOnOverlayClick={false}
    >
      <IconButton
        className="absolute right-[12px] top-[12px] cursor-pointer duration-300 hover:bg-neutral-200"
        nameIcon="x-close"
        sizeIcon={16}
        colorIcon={colors.neutral[500]}
        onClick={handleBack}
      />
      {isContact ? (
        <ContactForm
          contactForm={contactForm}
          setContactForm={setContactForm}
          emailError={emailError}
          setEmailError={setEmailError}
          nameError={nameError}
          setNameError={setNameError}
          phoneError={phoneError}
          setPhoneError={setPhoneError}
          isSending={isSending}
          handleSendContact={handleSendContact}
          handleEmailValidation={handleEmailValidation}
        />
      ) : (
        <ListPlan
          selectedPlan={selectedPlan}
          setSelectedPlan={setSelectedPlan}
          plans={plans}
          handleContact={handleContact}
        />
      )}
    </BaseModal>
  )
}

export default UpdateSubscription
